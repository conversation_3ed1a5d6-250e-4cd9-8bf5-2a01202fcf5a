#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/BrainrotDuel/node_modules/.pnpm/http-server@14.1.1/node_modules/http-server/bin/node_modules:/Users/<USER>/BrainrotDuel/node_modules/.pnpm/http-server@14.1.1/node_modules/http-server/node_modules:/Users/<USER>/BrainrotDuel/node_modules/.pnpm/http-server@14.1.1/node_modules:/Users/<USER>/BrainrotDuel/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/BrainrotDuel/node_modules/.pnpm/http-server@14.1.1/node_modules/http-server/bin/node_modules:/Users/<USER>/BrainrotDuel/node_modules/.pnpm/http-server@14.1.1/node_modules/http-server/node_modules:/Users/<USER>/BrainrotDuel/node_modules/.pnpm/http-server@14.1.1/node_modules:/Users/<USER>/BrainrotDuel/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/http-server" "$@"
else
  exec node  "$basedir/../../bin/http-server" "$@"
fi
