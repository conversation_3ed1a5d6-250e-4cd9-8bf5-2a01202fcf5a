<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>意大利脑残山海经格斗游戏</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
</head>
<body>
    <div id="game-container">
        <!-- 游戏菜单 -->
        <div id="main-menu" class="screen">
            <h1>🇮🇹 意大利脑残山海经格斗游戏 🥊</h1>
            <div class="menu-buttons">
                <button id="start-game-btn" class="menu-btn">开始游戏</button>
                <button id="how-to-play-btn" class="menu-btn">游戏说明</button>
            </div>
        </div>

        <!-- 角色选择界面 -->
        <div id="character-select" class="screen hidden">
            <h2>选择你的角色</h2>
            <div class="character-grid">
                <div class="character-card" data-character="part_01">
                    <img src="assets/part_01.png" alt="角色1">
                    <h3>🍝 意大利面条侠</h3>
                    <p>速度快，攻击灵活</p>
                </div>
                <div class="character-card" data-character="part_02">
                    <img src="assets/part_02.png" alt="角色2">
                    <h3>🍕 披萨战士</h3>
                    <p>攻击力强，防御高</p>
                </div>
                <div class="character-card" data-character="part_03">
                    <img src="assets/part_03.png" alt="角色3">
                    <h3>🧄 大蒜守护者</h3>
                    <p>防御专精，反击强</p>
                </div>
                <div class="character-card" data-character="part_04">
                    <img src="assets/part_04.png" alt="角色4">
                    <h3>☕ 意式咖啡师</h3>
                    <p>特殊技能多，平衡型</p>
                </div>
                <div class="character-card" data-character="part_05">
                    <img src="assets/part_05.png" alt="角色5">
                    <h3>🍷 红酒骑士</h3>
                    <p>优雅战斗，技巧型</p>
                </div>
                <div class="character-card" data-character="part_06">
                    <img src="assets/part_06.png" alt="角色6">
                    <h3>🧀 奶酪法师</h3>
                    <p>魔法攻击，远程型</p>
                </div>
                <div class="character-card" data-character="part_07">
                    <img src="assets/part_07.png" alt="角色7">
                    <h3>🥖 法棍剑客</h3>
                    <p>剑术精湛，连击强</p>
                </div>
                <div class="character-card" data-character="part_08">
                    <img src="assets/part_08.png" alt="角色8">
                    <h3>🍯 蜂蜜治疗师</h3>
                    <p>回复能力，辅助型</p>
                </div>
                <div class="character-card" data-character="part_09">
                    <img src="assets/part_09.png" alt="角色9">
                    <h3>🌶️ 辣椒火神</h3>
                    <p>火焰攻击，爆发强</p>
                </div>
                <div class="character-card" data-character="part_10">
                    <img src="assets/part_10.png" alt="角色10">
                    <h3>🍄 蘑菇萨满</h3>
                    <p>神秘力量，控制型</p>
                </div>
                <div class="character-card" data-character="part_11">
                    <img src="assets/part_11.png" alt="角色11">
                    <h3>🎭 面具刺客</h3>
                    <p>隐秘暗杀，敏捷型</p>
                </div>
                <div class="character-card" data-character="part_12">
                    <img src="assets/part_01.png" alt="角色12">
                    <h3>🤪 见啊实打实打双打啊啊啊啊啊啊啊啊</h3>
                    <p>脑残之王，混乱型</p>
                </div>
            </div>
            <div class="player-info">
                <input type="text" id="player-name" placeholder="输入你的名字" maxlength="20">
                <div id="current-selection" class="current-selection">
                    <span class="selection-hint">👆 请选择一个角色</span>
                </div>
                <button id="join-game-btn" class="btn" disabled>加入游戏</button>
                <button id="back-to-menu-btn" class="btn secondary">返回菜单</button>
            </div>
        </div>

        <!-- 等待界面 -->
        <div id="waiting-room" class="screen hidden">
            <h2>等待对手加入...</h2>
            <div class="waiting-animation">
                <div class="spinner"></div>
            </div>
            <p>房间ID: <span id="room-id"></span></p>
            <button id="cancel-waiting-btn" class="menu-btn">取消等待</button>
        </div>

        <!-- 游戏界面 -->
        <div id="game-screen" class="screen hidden">
            <div id="game-ui">
                <div class="player-hud player1-hud">
                    <div class="player-name" id="player1-name">玩家1</div>
                    <div class="health-bar">
                        <div class="health-fill" id="player1-health"></div>
                    </div>
                </div>
                <div class="game-timer" id="game-timer">99</div>
                <div class="player-hud player2-hud">
                    <div class="player-name" id="player2-name">玩家2</div>
                    <div class="health-bar">
                        <div class="health-fill" id="player2-health"></div>
                    </div>
                </div>
            </div>
            <!-- Phaser游戏画布将在这里动态创建 -->
            <div id="game-canvas-container"></div>
            <div id="controls-hint">
                <p><strong>基础控制:</strong> WASD移动 | J拳击 | K踢腿 | L特殊攻击</p>
                <p><strong>技能系统:</strong> 1-9数字键使用对应技能图片 (skill-01 到 skill-09)</p>
                <p><strong>防御系统:</strong> 空格键防御</p>
                <p class="attack-hint">💡 提示: 连续攻击可以形成连击！防御和闪避可以避免伤害！</p>
                <button id="mute-btn" class="sound-btn">🔊</button>
            </div>
        </div>

        <!-- 游戏结束界面 -->
        <div id="game-over" class="screen hidden">
            <h2 id="game-result">游戏结束</h2>
            <div id="winner-display"></div>
            <div class="menu-buttons">
                <button id="play-again-btn" class="menu-btn">再来一局</button>
                <button id="back-to-menu-btn" class="menu-btn">返回主菜单</button>
            </div>
        </div>

        <!-- 游戏说明 -->
        <div id="how-to-play" class="screen hidden">
            <h2>游戏说明</h2>
            <div class="instructions">
                <h3>基础控制:</h3>
                <ul>
                    <li>W/A/S/D - 移动角色</li>
                    <li>J - 拳击攻击 (伤害: 15)</li>
                    <li>K - 踢腿攻击 (伤害: 20)</li>
                    <li>L - 特殊攻击 (伤害: 30)</li>
                    <li>1 - 随机技能 (导弹/火焰/冰块)</li>
                </ul>
                <h3>高级格斗系统:</h3>
                <ul>
                    <li>W+J - 上勾拳连击</li>
                    <li>S+K - 下扫腿连击</li>
                    <li>A/D+L - 侧身特殊攻击</li>
                    <li>空格键 - 防御 (减少50%伤害)</li>
                    <li>Shift+方向键 - 闪避</li>
                    <li>连续攻击 - 形成连击 (最多5连击)</li>
                </ul>
                <h3>技能系统 (数字键1-9):</h3>
                <ul>
                    <li>1 - 🎯 技能01 (skill-01图片)</li>
                    <li>2 - 🎯 技能02 (skill-02图片)</li>
                    <li>3 - 🎯 技能03 (skill-03图片)</li>
                    <li>4 - 🎯 技能04 (skill-04图片)</li>
                    <li>5 - 🎯 技能05 (skill-05图片)</li>
                    <li>6 - 🎯 技能06 (skill-06图片)</li>
                    <li>7 - 🎯 技能07 (skill-07图片)</li>
                    <li>8 - 🎯 技能08 (skill-08图片)</li>
                    <li>9 - 🎯 技能09 (skill-09图片)</li>
                </ul>
                <h3>游戏规则:</h3>
                <ul>
                    <li>每个角色有100点生命值</li>
                    <li>连击可以增加伤害 (每连击+20%伤害)</li>
                    <li>防御可以减少伤害，闪避可以完全避免伤害</li>
                    <li>合理使用特殊技能可以扭转战局</li>
                    <li>率先击败对手的玩家获胜</li>
                    <li>享受搞笑的意大利脑残风格！</li>
                </ul>
            </div>
            <button id="back-from-instructions-btn" class="menu-btn">返回</button>
        </div>
    </div>

    <!-- 引入Socket.IO -->
    <script src="/socket.io/socket.io.js"></script>
    <!-- 引入Phaser游戏脚本 -->
    <script src="src/phaser-game.js"></script>
    <script src="src/phaser-main.js"></script>
    <!-- 保留原始游戏脚本作为备份 -->
    <!-- <script src="src/game.js"></script> -->
</body>
</html>
