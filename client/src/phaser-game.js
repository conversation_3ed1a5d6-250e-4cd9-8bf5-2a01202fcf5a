// Phaser.js版本的意大利脑残山海经格斗游戏

class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });
        this.socket = null;
        this.players = {};
        this.localPlayer = null;
        this.cursors = null;
        this.attackKeys = null;
        this.skillKeys = null;
        this.activeSkills = [];
        this.soundManager = null;
    }

    preload() {
        console.log('开始加载游戏资源...');

        // 加载角色图片
        const characters = ['part_01', 'part_02', 'part_03', 'part_04', 'part_05', 'part_06',
                          'part_07', 'part_08', 'part_09', 'part_10', 'part_11', 'part_12'];

        characters.forEach(char => {
            this.load.image(char, `./assets/${char}.png`);
            console.log(`加载角色图片: ${char} from ./assets/${char}.png`);
        });

        // 加载技能图片（如果存在的话）
        this.load.image('skill-01', './assets/skill-01.png');
        this.load.image('skill-02', './assets/skill-02.png');
        this.load.image('skill-03', './assets/skill-03.png');

        // 创建简单的像素图形作为占位符
        this.load.image('ground', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');

        // 创建备用角色图片（简单的彩色方块）
        this.load.image('fallback-player', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mNk+M9QjxdgGBUYNYBhDQA+/AH/0MQgNwAAAABJRU5ErkJggg==');

        // 添加加载事件监听
        this.load.on('filecomplete', (key, type, data) => {
            console.log(`资源加载完成: ${key} (${type})`);
        });

        this.load.on('loaderror', (file) => {
            console.error(`资源加载失败: ${file.key} - ${file.src}`);
        });

        this.load.on('complete', () => {
            console.log('所有资源加载完成');
            this.testImageUrls();
        });
    }

    testImageUrls() {
        // 测试图片URL是否可访问
        const testImg = new Image();
        testImg.onload = () => {
            console.log('✓ 图片URL测试成功: ./assets/part_01.png');
        };
        testImg.onerror = () => {
            console.error('✗ 图片URL测试失败: ./assets/part_01.png');
            console.log('尝试其他路径...');

            // 尝试不同的路径
            const testImg2 = new Image();
            testImg2.onload = () => console.log('✓ 备用路径成功: assets/part_01.png');
            testImg2.onerror = () => console.error('✗ 备用路径也失败: assets/part_01.png');
            testImg2.src = 'assets/part_01.png';
        };
        testImg.src = './assets/part_01.png';
    }

    create() {
        console.log('🎮 开始创建Phaser游戏场景...');
        console.log('🔧 场景状态:', {
            key: this.scene.key,
            isActive: this.scene.isActive(),
            isVisible: this.scene.isVisible()
        });

        // 设置世界边界
        this.physics.world.setBounds(0, 0, 800, 600);
        console.log('✅ 世界边界设置完成');

        // 创建背景
        this.createBackground();
        console.log('背景创建完成');

        // 设置输入控制
        this.setupControls();
        console.log('输入控制设置完成');

        // 初始化Socket连接
        this.initSocket();
        console.log('Socket连接初始化完成');

        // 初始化音效
        this.initSoundManager();
        console.log('音效管理器初始化完成');

        // 创建备用角色纹理
        this.createFallbackTextures();

        // 禁用测试精灵以避免背景闪烁
        // this.addTestSprites();

        console.log('🎮 Phaser游戏场景创建完成');
        console.log('场景对象:', this);
        console.log('物理世界:', this.physics.world);

        // 禁用测试元素以避免背景闪烁
        // this.showTestElements();

        // 不自动创建测试玩家，等待phaser-main.js创建
        // this.createTestPlayers();

        console.log('🎉 GameScene.create() 方法执行完成！');
        console.log('🎮 游戏场景已完全初始化');

        // 添加基础键盘测试
        this.addBasicKeyboardTest();

        // 确保游戏画布获得焦点
        this.enableKeyboardFocus();

        // 强制创建测试玩家以确保游戏可以立即开始
        setTimeout(() => {
            this.forceCreateTestPlayer();
        }, 1000);
    }

    addBasicKeyboardTest() {
        console.log('🔧 添加基础键盘测试...');

        // 检查Phaser输入系统状态
        console.log('🔍 Phaser输入系统状态:', {
            hasInput: !!this.input,
            hasKeyboard: !!this.input?.keyboard,
            sceneActive: this.scene.isActive(),
            sceneVisible: this.scene.isVisible()
        });

        if (!this.input || !this.input.keyboard) {
            console.error('❌ Phaser键盘输入系统不可用');
            return;
        }

        // 添加全局键盘事件监听器
        document.addEventListener('keydown', (event) => {
            console.log('🌍 场景内全局键盘事件 - KeyDown:', {
                key: event.key,
                code: event.code,
                timestamp: Date.now(),
                target: event.target.tagName
            });
        });

        document.addEventListener('keyup', (event) => {
            console.log('🌍 场景内全局键盘事件 - KeyUp:', {
                key: event.key,
                code: event.code,
                timestamp: Date.now(),
                target: event.target.tagName
            });
        });

        // 添加Phaser特定的键盘事件监听器
        try {
            this.input.keyboard.on('keydown', (event) => {
                console.log('🎮 Phaser键盘事件 - KeyDown:', {
                    key: event.key,
                    code: event.code,
                    timestamp: Date.now()
                });

                // 直接测试移动
                this.testDirectMovement(event.key);
            });

            this.input.keyboard.on('keyup', (event) => {
                console.log('🎮 Phaser键盘事件 - KeyUp:', {
                    key: event.key,
                    code: event.code,
                    timestamp: Date.now()
                });
            });

            console.log('✅ Phaser键盘事件监听器设置成功');
        } catch (error) {
            console.error('❌ 设置Phaser键盘事件监听器失败:', error);
        }

        console.log('✅ 基础键盘测试设置完成');
        console.log('💡 请按任意键测试键盘事件是否正常工作');

        // 强制测试键盘系统
        setTimeout(() => {
            console.log('🧪 强制测试键盘系统...');
            console.log('🔍 当前键盘对象状态:', {
                cursors: !!this.cursors,
                wasd: !!this.wasd,
                attackKeys: !!this.attackKeys,
                skillKeys: !!this.skillKeys
            });
        }, 1000);
    }

    enableKeyboardFocus() {
        console.log('🎯 启用键盘焦点...');

        try {
            // 确保游戏画布可以接收键盘事件
            const canvas = this.game.canvas;
            if (canvas) {
                canvas.setAttribute('tabindex', '0');
                canvas.focus();
                console.log('✅ 游戏画布已获得焦点');

                // 添加点击事件来重新获得焦点
                canvas.addEventListener('click', () => {
                    canvas.focus();
                    console.log('🖱️ 点击画布，重新获得焦点');
                });
            }

            // 启用Phaser的键盘捕获
            this.input.keyboard.enabled = true;
            this.input.keyboard.enableGlobalCapture();
            console.log('✅ Phaser键盘捕获已启用');

        } catch (error) {
            console.error('❌ 启用键盘焦点失败:', error);
        }
    }

    testDirectMovement(key) {
        // 直接测试移动功能
        console.log('🧪 测试直接移动，按键:', key);

        // 查找任何可用的玩家进行移动测试
        let testPlayer = this.localPlayer;
        if (!testPlayer) {
            const playerIds = Object.keys(this.players || {});
            if (playerIds.length > 0) {
                testPlayer = this.players[playerIds[0]];
                console.log('🎯 使用第一个玩家进行测试:', playerIds[0]);
            }
        }

        if (!testPlayer || !testPlayer.sprite) {
            console.log('❌ 没有可用的玩家进行移动测试');
            return;
        }

        const moveDistance = 20;
        let newX = testPlayer.sprite.x;
        let newY = testPlayer.sprite.y;

        switch (key.toLowerCase()) {
            case 'w':
            case 'arrowup':
                newY = Math.max(50, newY - moveDistance);
                console.log('⬆️ 直接向上移动');
                break;
            case 's':
            case 'arrowdown':
                newY = Math.min(550, newY + moveDistance);
                console.log('⬇️ 直接向下移动');
                break;
            case 'a':
            case 'arrowleft':
                newX = Math.max(50, newX - moveDistance);
                console.log('⬅️ 直接向左移动');
                break;
            case 'd':
            case 'arrowright':
                newX = Math.min(750, newX + moveDistance);
                console.log('➡️ 直接向右移动');
                break;
            default:
                return; // 不是移动键，直接返回
        }

        try {
            testPlayer.sprite.setPosition(newX, newY);
            console.log('✅ 直接移动成功:', {
                from: { x: testPlayer.sprite.x, y: testPlayer.sprite.y },
                to: { x: newX, y: newY }
            });
        } catch (error) {
            console.error('❌ 直接移动失败:', error);
        }
    }

    handlePageLevelMovement(key) {
        // 页面级移动处理
        console.log('🌍 页面级移动处理，按键:', key);

        // 查找本地玩家
        let player = this.localPlayer;
        if (!player) {
            const playerIds = Object.keys(this.players || {});
            if (playerIds.length > 0) {
                player = this.players[playerIds[0]];
                // 设置为本地玩家
                this.localPlayer = player;
                console.log('🎯 自动设置本地玩家:', playerIds[0]);
            }
        }

        if (!player || !player.sprite) {
            console.log('❌ 没有可用的玩家进行移动');
            return;
        }

        const moveDistance = 20;
        let newX = player.sprite.x;
        let newY = player.sprite.y;
        let moved = false;

        switch (key.toLowerCase()) {
            case 'w':
            case 'arrowup':
                newY = Math.max(50, newY - moveDistance);
                moved = true;
                console.log('⬆️ 页面级向上移动');
                break;
            case 's':
            case 'arrowdown':
                newY = Math.min(550, newY + moveDistance);
                moved = true;
                console.log('⬇️ 页面级向下移动');
                break;
            case 'a':
            case 'arrowleft':
                newX = Math.max(50, newX - moveDistance);
                moved = true;
                player.facing = 'left';
                console.log('⬅️ 页面级向左移动');
                break;
            case 'd':
            case 'arrowright':
                newX = Math.min(750, newX + moveDistance);
                moved = true;
                player.facing = 'right';
                console.log('➡️ 页面级向右移动');
                break;
        }

        if (moved) {
            try {
                player.sprite.setPosition(newX, newY);
                if (player.facing) {
                    player.sprite.setFlipX(player.facing === 'left');
                }

                // 更新血条和名字位置
                if (player.healthBarBg) player.healthBarBg.setPosition(newX, newY - 80);
                if (player.healthBar) player.healthBar.setPosition(newX, newY - 80);
                if (player.nameText) player.nameText.setPosition(newX, newY - 100);

                console.log('✅ 页面级移动成功:', {
                    from: { x: player.sprite.x, y: player.sprite.y },
                    to: { x: newX, y: newY },
                    facing: player.facing
                });
            } catch (error) {
                console.error('❌ 页面级移动失败:', error);
            }
        }

        // 处理攻击和技能按键
        this.handlePageLevelCombat(key, player);
    }

    handlePageLevelCombat(key, player) {
        if (!player || !player.sprite) return;

        switch (key.toLowerCase()) {
            case 'j':
                console.log('🥊 页面级拳击攻击！');
                this.performPageLevelAttack('punch', player);
                break;
            case 'k':
                console.log('🦵 页面级踢腿攻击！');
                this.performPageLevelAttack('kick', player);
                break;
            case 'l':
                console.log('⚡ 页面级特殊攻击！');
                this.performPageLevelAttack('special', player);
                break;
            case '1':
                console.log('🎯 页面级技能01！');
                this.performPageLevelSkillImage(1, player);
                break;
            case '2':
                console.log('🎯 页面级技能02！');
                this.performPageLevelSkillImage(2, player);
                break;
            case '3':
                console.log('🎯 页面级技能03！');
                this.performPageLevelSkillImage(3, player);
                break;
            case '4':
                console.log('🎯 页面级技能04！');
                this.performPageLevelSkillImage(4, player);
                break;
            case '5':
                console.log('🎯 页面级技能05！');
                this.performPageLevelSkillImage(5, player);
                break;
            case '6':
                console.log('🎯 页面级技能06！');
                this.performPageLevelSkillImage(6, player);
                break;
            case '7':
                console.log('🎯 页面级技能07！');
                this.performPageLevelSkillImage(7, player);
                break;
            case '8':
                console.log('🎯 页面级技能08！');
                this.performPageLevelSkillImage(8, player);
                break;
            case '9':
                console.log('🎯 页面级技能09！');
                this.performPageLevelSkillImage(9, player);
                break;
            case ' ':
            case 'space':
                console.log('🛡️ 页面级防御！');
                this.handlePageLevelDefense(player);
                break;
        }
    }

    performPageLevelAttack(attackType, player) {
        if (player.isAttacking) return;

        player.isAttacking = true;

        // 播放攻击动画
        this.playAttackAnimation(player, attackType);

        // 检查攻击是否击中目标
        this.checkAttackHit(player, attackType);

        // 重置攻击状态
        setTimeout(() => {
            if (player) {
                player.isAttacking = false;
            }
        }, 500);
    }

    checkAttackHit(attacker, attackType) {
        // 攻击范围
        const attackRange = 80;

        // 攻击伤害
        const attackDamage = {
            'punch': 15,
            'kick': 20,
            'special': 30
        };

        const damage = attackDamage[attackType] || 15;

        // 检查范围内的目标
        Object.values(this.players || {}).forEach(player => {
            if (player !== attacker && player.sprite) {
                const distance = Phaser.Math.Distance.Between(
                    attacker.sprite.x, attacker.sprite.y,
                    player.sprite.x, player.sprite.y
                );

                if (distance <= attackRange) {
                    console.log(`👊 ${attacker.name} 的${attackType}攻击击中了 ${player.name}！造成 ${damage} 点伤害！`);

                    // 造成伤害
                    this.dealDamageToPlayer(player, damage);

                    // 击退效果
                    const knockbackDistance = 30;
                    const direction = attacker.facing === 'right' ? 1 : -1;
                    const newX = Math.max(50, Math.min(750, player.sprite.x + knockbackDistance * direction));

                    this.tweens.add({
                        targets: player.sprite,
                        x: newX,
                        duration: 200,
                        ease: 'Power2'
                    });

                    // 更新血条位置
                    if (player.healthBarBg) {
                        this.tweens.add({
                            targets: player.healthBarBg,
                            x: newX,
                            duration: 200
                        });
                    }
                    if (player.healthBar) {
                        this.tweens.add({
                            targets: player.healthBar,
                            x: newX,
                            duration: 200
                        });
                    }
                    if (player.nameText) {
                        this.tweens.add({
                            targets: player.nameText,
                            x: newX,
                            duration: 200
                        });
                    }
                }
            }
        });
    }

    performPageLevelSkill(player) {
        if (player.isUsingSkill) return;

        player.isUsingSkill = true;
        const skillType = this.getRandomSkill();

        // 创建技能特效
        this.createSkillEffect(skillType, player.sprite.x, player.sprite.y, player.facing);

        // 重置技能状态
        setTimeout(() => {
            if (player) {
                player.isUsingSkill = false;
            }
        }, 1000);
    }

    performPageLevelSkillImage(skillNumber, player) {
        if (player.isUsingSkill) return;

        player.isUsingSkill = true;

        console.log(`🎯 使用技能${skillNumber.toString().padStart(2, '0')}！`);

        // 使用对应的技能图片
        this.createSkillImageEffect(skillNumber, player.sprite.x, player.sprite.y, player.facing);

        // 重置技能状态
        setTimeout(() => {
            if (player) {
                player.isUsingSkill = false;
            }
        }, 1500);
    }

    createSkillImageEffect(skillNumber, x, y, facing) {
        // 格式化技能图片名称 (skill-01, skill-02, skill-03)
        const skillImageKey = `skill-${skillNumber.toString().padStart(2, '0')}`;

        console.log(`💣 发射炮弹技能: ${skillImageKey}`);

        // 检查技能图片是否存在
        if (!this.textures.exists(skillImageKey)) {
            console.warn(`⚠️ 技能图片不存在: ${skillImageKey}，使用默认炮弹`);
            // 如果图片不存在，使用默认的炮弹特效
            this.createDefaultCannonball(skillNumber, x, y, facing);
            return;
        }

        // 找到目标（对手）
        const target = this.findTarget(x, y);

        // 创建炮弹（使用技能图片）
        const cannonball = this.add.image(x, y - 30, skillImageKey);
        cannonball.setScale(0.3);
        cannonball.setDepth(45);

        // 根据面向方向调整初始位置
        const startOffsetX = facing === 'right' ? 50 : -50;
        cannonball.setPosition(x + startOffsetX, y - 30);

        // 计算目标位置
        let targetX, targetY;
        if (target) {
            targetX = target.sprite.x;
            targetY = target.sprite.y;
            console.log(`🎯 锁定目标: ${target.name} 位置(${targetX}, ${targetY})`);
        } else {
            // 如果没有目标，向前方发射
            targetX = facing === 'right' ? x + 300 : x - 300;
            targetY = y + (Math.random() - 0.5) * 100; // 随机偏移
            console.log(`🎯 向前方发射: 位置(${targetX}, ${targetY})`);
        }

        // 炮弹飞行轨迹（抛物线）
        const distance = Math.abs(targetX - cannonball.x);
        const flightTime = Math.min(1500, distance * 2); // 飞行时间

        // 添加炮弹拖尾效果
        this.createCannonballTrail(cannonball);

        // 炮弹飞行动画
        this.tweens.add({
            targets: cannonball,
            x: targetX,
            duration: flightTime,
            ease: 'Power2'
        });

        this.tweens.add({
            targets: cannonball,
            y: targetY,
            duration: flightTime,
            ease: 'Bounce.easeOut',
            onComplete: () => {
                // 炮弹到达目标，创建爆炸效果
                this.createExplosion(targetX, targetY, skillNumber, skillImageKey);
                cannonball.destroy();
            }
        });

        // 炮弹旋转
        this.tweens.add({
            targets: cannonball,
            rotation: Math.PI * 4,
            duration: flightTime,
            ease: 'Linear'
        });

        // 发射音效提示
        const launchText = this.add.text(x, y - 80, `💣 发射技能${skillNumber}!`, {
            fontSize: '14px',
            fill: '#FF4500',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);
        launchText.setDepth(51);

        // 发射文字动画
        this.tweens.add({
            targets: launchText,
            y: launchText.y - 20,
            alpha: 0,
            duration: 800,
            onComplete: () => {
                launchText.destroy();
            }
        });
    }

    createDefaultSkillEffect(skillNumber, x, y, facing) {
        // 默认技能特效（当图片不存在时使用）
        const colors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4, 0xFECA57, 0xFF9FF3, 0x54A0FF, 0x5F27CD, 0x00D2D3];
        const color = colors[skillNumber - 1] || 0xFFD700;

        const effectCircle = this.add.circle(x, y - 50, 30, color, 0.8);
        effectCircle.setStrokeStyle(3, 0xFFFFFF);
        effectCircle.setDepth(50);

        // 默认动画
        this.tweens.add({
            targets: effectCircle,
            scaleX: 2,
            scaleY: 2,
            alpha: 0,
            duration: 800,
            onComplete: () => {
                effectCircle.destroy();
            }
        });

        console.log(`✨ 使用默认特效替代技能${skillNumber}`);
    }

    findTarget(playerX, playerY) {
        // 找到最近的敌人作为目标
        let closestTarget = null;
        let closestDistance = Infinity;

        Object.values(this.players || {}).forEach(player => {
            if (player !== this.localPlayer && player.sprite) {
                const distance = Phaser.Math.Distance.Between(
                    playerX, playerY,
                    player.sprite.x, player.sprite.y
                );

                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestTarget = player;
                }
            }
        });

        return closestTarget;
    }

    createCannonballTrail(cannonball) {
        // 创建炮弹拖尾效果
        const trail = [];
        const trailLength = 5;

        // 创建拖尾粒子
        for (let i = 0; i < trailLength; i++) {
            const trailParticle = this.add.circle(
                cannonball.x,
                cannonball.y,
                3 - i * 0.5,
                0xFFAA00,
                0.8 - i * 0.15
            );
            trailParticle.setDepth(40);
            trail.push(trailParticle);
        }

        // 更新拖尾位置
        const updateTrail = () => {
            for (let i = trail.length - 1; i > 0; i--) {
                if (trail[i] && trail[i-1]) {
                    trail[i].setPosition(trail[i-1].x, trail[i-1].y);
                }
            }
            if (trail[0] && cannonball.active) {
                trail[0].setPosition(cannonball.x, cannonball.y);
            }
        };

        // 定时更新拖尾
        const trailTimer = this.time.addEvent({
            delay: 50,
            callback: updateTrail,
            repeat: -1
        });

        // 炮弹销毁时清理拖尾
        cannonball.on('destroy', () => {
            trailTimer.destroy();
            trail.forEach(particle => {
                if (particle && particle.active) {
                    this.tweens.add({
                        targets: particle,
                        alpha: 0,
                        duration: 200,
                        onComplete: () => particle.destroy()
                    });
                }
            });
        });
    }

    createExplosion(x, y, skillNumber, skillImageKey) {
        console.log(`💥 在位置(${x}, ${y})创建爆炸效果！`);

        // 主爆炸效果
        const mainExplosion = this.add.circle(x, y, 10, 0xFF4500, 0.9);
        mainExplosion.setStrokeStyle(3, 0xFFFF00);
        mainExplosion.setDepth(55);

        // 爆炸扩散动画
        this.tweens.add({
            targets: mainExplosion,
            scaleX: 8,
            scaleY: 8,
            alpha: 0,
            duration: 600,
            ease: 'Power2',
            onComplete: () => mainExplosion.destroy()
        });

        // 创建爆炸碎片
        for (let i = 0; i < 12; i++) {
            const fragment = this.add.circle(x, y, 3, 0xFF6600, 0.8);
            fragment.setDepth(54);

            const angle = (i / 12) * Math.PI * 2;
            const distance = 80 + Math.random() * 40;
            const targetX = x + Math.cos(angle) * distance;
            const targetY = y + Math.sin(angle) * distance;

            this.tweens.add({
                targets: fragment,
                x: targetX,
                y: targetY,
                alpha: 0,
                duration: 400 + Math.random() * 200,
                ease: 'Power2',
                onComplete: () => fragment.destroy()
            });
        }

        // 技能图片爆炸效果
        if (this.textures.exists(skillImageKey)) {
            const skillExplosion = this.add.image(x, y, skillImageKey);
            skillExplosion.setScale(0.5);
            skillExplosion.setDepth(56);
            skillExplosion.setTint(0xFF4500);

            this.tweens.add({
                targets: skillExplosion,
                scaleX: 2,
                scaleY: 2,
                alpha: 0,
                rotation: Math.PI,
                duration: 800,
                onComplete: () => skillExplosion.destroy()
            });
        }

        // 爆炸文字
        const explosionText = this.add.text(x, y - 60, `💥 轰炸！`, {
            fontSize: '20px',
            fill: '#FF4500',
            fontStyle: 'bold',
            stroke: '#FFFF00',
            strokeThickness: 2
        }).setOrigin(0.5);
        explosionText.setDepth(57);

        this.tweens.add({
            targets: explosionText,
            y: explosionText.y - 40,
            scaleX: 1.5,
            scaleY: 1.5,
            alpha: 0,
            duration: 1000,
            onComplete: () => explosionText.destroy()
        });

        // 屏幕剧烈震动
        this.cameras.main.shake(500, 0.03);

        // 检查是否击中目标
        this.checkExplosionDamage(x, y, skillNumber);
    }

    createDefaultCannonball(skillNumber, x, y, facing) {
        // 默认炮弹（当技能图片不存在时使用）
        const colors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4, 0xFECA57, 0xFF9FF3, 0x54A0FF, 0x5F27CD, 0x00D2D3];
        const color = colors[skillNumber - 1] || 0xFFD700;

        console.log(`💣 发射默认炮弹: 技能${skillNumber}`);

        // 找到目标
        const target = this.findTarget(x, y);

        // 创建默认炮弹
        const cannonball = this.add.circle(x, y - 30, 12, color, 0.9);
        cannonball.setStrokeStyle(2, 0xFFFFFF);
        cannonball.setDepth(45);

        // 根据面向方向调整初始位置
        const startOffsetX = facing === 'right' ? 50 : -50;
        cannonball.setPosition(x + startOffsetX, y - 30);

        // 计算目标位置
        let targetX, targetY;
        if (target) {
            targetX = target.sprite.x;
            targetY = target.sprite.y;
        } else {
            targetX = facing === 'right' ? x + 300 : x - 300;
            targetY = y + (Math.random() - 0.5) * 100;
        }

        // 炮弹飞行
        const distance = Math.abs(targetX - cannonball.x);
        const flightTime = Math.min(1500, distance * 2);

        this.createCannonballTrail(cannonball);

        this.tweens.add({
            targets: cannonball,
            x: targetX,
            y: targetY,
            duration: flightTime,
            ease: 'Power2',
            onComplete: () => {
                this.createExplosion(targetX, targetY, skillNumber, null);
                cannonball.destroy();
            }
        });
    }

    checkExplosionDamage(explosionX, explosionY, skillNumber) {
        // 检查爆炸范围内的目标
        const explosionRadius = 80;
        const damage = 15 + skillNumber * 5; // 技能编号越高伤害越大

        Object.values(this.players || {}).forEach(player => {
            if (player !== this.localPlayer && player.sprite) {
                const distance = Phaser.Math.Distance.Between(
                    explosionX, explosionY,
                    player.sprite.x, player.sprite.y
                );

                if (distance <= explosionRadius) {
                    console.log(`💥 击中目标 ${player.name}！造成 ${damage} 点伤害！`);

                    // 真正扣除血量
                    this.dealDamageToPlayer(player, damage);

                    // 显示伤害数字
                    const damageText = this.add.text(
                        player.sprite.x,
                        player.sprite.y - 50,
                        `-${damage}`,
                        {
                            fontSize: '18px',
                            fill: '#FF0000',
                            fontStyle: 'bold',
                            stroke: '#FFFFFF',
                            strokeThickness: 2
                        }
                    ).setOrigin(0.5);
                    damageText.setDepth(60);

                    this.tweens.add({
                        targets: damageText,
                        y: damageText.y - 30,
                        alpha: 0,
                        duration: 1000,
                        onComplete: () => damageText.destroy()
                    });

                    // 目标受击效果
                    this.tweens.add({
                        targets: player.sprite,
                        tint: 0xFF0000,
                        duration: 100,
                        yoyo: true,
                        repeat: 2,
                        onComplete: () => {
                            player.sprite.clearTint();
                        }
                    });
                }
            }
        });
    }

    dealDamageToPlayer(player, damage) {
        // 扣除玩家血量
        if (!player.health) {
            player.health = 100; // 如果没有血量，设置为100
        }

        const oldHealth = player.health;
        player.health = Math.max(0, player.health - damage);

        console.log(`🩸 ${player.name} 血量: ${oldHealth} → ${player.health} (受到${damage}点伤害)`);

        // 更新血条显示
        this.updateHealthBar(player);

        // 检查是否死亡
        if (player.health <= 0) {
            console.log(`💀 ${player.name} 被击败了！`);
            this.handlePlayerDeath(player);
        }
    }

    updateHealthBar(player) {
        if (!player.healthBar || !player.healthBarBg) {
            console.log(`⚠️ ${player.name} 没有血条，重新创建`);
            this.createHealthBar(player);
            return;
        }

        // 计算血条宽度
        const maxWidth = 80;
        const healthPercentage = Math.max(0, player.health / 100);
        const newWidth = maxWidth * healthPercentage;

        console.log(`🩸 更新 ${player.name} 血条: ${healthPercentage * 100}% (宽度: ${newWidth})`);

        // 更新血条宽度
        player.healthBar.width = newWidth;

        // 根据血量改变血条颜色
        if (healthPercentage > 0.6) {
            player.healthBar.fillColor = 0x4CAF50; // 绿色
        } else if (healthPercentage > 0.3) {
            player.healthBar.fillColor = 0xFFC107; // 黄色
        } else {
            player.healthBar.fillColor = 0xF44336; // 红色
        }

        // 血条闪烁效果
        this.tweens.add({
            targets: player.healthBar,
            alpha: 0.5,
            duration: 100,
            yoyo: true,
            repeat: 1
        });
    }

    createHealthBar(player) {
        if (!player.sprite) return;

        console.log(`🩸 为 ${player.name} 创建血条`);

        // 移除旧的血条
        if (player.healthBarBg) player.healthBarBg.destroy();
        if (player.healthBar) player.healthBar.destroy();

        // 血条背景
        player.healthBarBg = this.add.rectangle(
            player.sprite.x,
            player.sprite.y - 80,
            82, 12,
            0x000000, 0.8
        );
        player.healthBarBg.setDepth(20);

        // 血条
        const healthPercentage = Math.max(0, (player.health || 100) / 100);
        player.healthBar = this.add.rectangle(
            player.sprite.x - 40 + (80 * healthPercentage / 2),
            player.sprite.y - 80,
            80 * healthPercentage, 12,
            0x4CAF50, 1
        );
        player.healthBar.setDepth(21);

        console.log(`✅ ${player.name} 血条创建完成，当前血量: ${player.health || 100}`);
    }

    handlePlayerDeath(player) {
        console.log(`💀 处理 ${player.name} 的死亡`);

        // 死亡动画
        this.tweens.add({
            targets: player.sprite,
            alpha: 0.3,
            scaleX: 0.8,
            scaleY: 0.8,
            tint: 0x666666,
            duration: 500
        });

        // 死亡文字
        const deathText = this.add.text(
            player.sprite.x,
            player.sprite.y - 120,
            '💀 KO!',
            {
                fontSize: '24px',
                fill: '#FF0000',
                fontStyle: 'bold',
                stroke: '#000000',
                strokeThickness: 3
            }
        ).setOrigin(0.5);
        deathText.setDepth(70);

        this.tweens.add({
            targets: deathText,
            y: deathText.y - 50,
            alpha: 0,
            duration: 2000,
            onComplete: () => deathText.destroy()
        });

        // 隐藏血条
        if (player.healthBar) player.healthBar.setVisible(false);
        if (player.healthBarBg) player.healthBarBg.setVisible(false);

        // 检查游戏是否结束
        setTimeout(() => {
            this.checkGameEnd();
        }, 1000);
    }

    checkGameEnd() {
        const alivePlayers = Object.values(this.players || {}).filter(p => (p.health || 100) > 0);

        if (alivePlayers.length <= 1) {
            const winner = alivePlayers[0];
            console.log(`🏆 游戏结束！获胜者: ${winner ? winner.name : '无'}`);

            // 显示游戏结束界面
            setTimeout(() => {
                this.showGameEndScreen(winner ? winner.name : '无人', winner === this.localPlayer);
            }, 2000);
        }
    }

    handlePageLevelDefense(player) {
        if (!player.isDefending) {
            console.log('🛡️ 页面级开始防御！');
            player.isDefending = true;
            this.showDefenseEffect();
        }
    }

    handlePageLevelKeyUp(key) {
        // 处理按键释放事件
        let player = this.localPlayer;
        if (!player) {
            const playerIds = Object.keys(this.players || {});
            if (playerIds.length > 0) {
                player = this.players[playerIds[0]];
            }
        }

        if (!player || !player.sprite) return;

        switch (key.toLowerCase()) {
            case ' ':
            case 'space':
                if (player.isDefending) {
                    console.log('🛡️ 页面级停止防御！');
                    player.isDefending = false;
                    this.hideDefenseEffect();
                }
                break;
        }
    }

    forceCreateTestPlayer() {
        console.log('🚀 检查本地玩家状态...');

        // 检查是否已经有本地玩家
        if (this.localPlayer && this.localPlayer.sprite) {
            console.log('✅ 本地玩家已存在:', this.localPlayer.id);
            console.log('🎮 玩家精灵状态:', {
                hasSprite: !!this.localPlayer.sprite,
                spriteX: this.localPlayer.sprite?.x,
                spriteY: this.localPlayer.sprite?.y,
                spriteVisible: this.localPlayer.sprite?.visible
            });
            return;
        }

        // 检查是否有玩家但没有设置为本地玩家
        const playerIds = Object.keys(this.players || {});
        console.log('🔍 当前玩家列表:', playerIds);

        if (playerIds.length > 0) {
            // 使用第一个玩家作为本地玩家
            const firstPlayerId = playerIds[0];
            this.localPlayer = this.players[firstPlayerId];
            console.log('✅ 设置现有玩家为本地玩家:', firstPlayerId);
            console.log('🎮 玩家精灵状态:', {
                hasSprite: !!this.localPlayer.sprite,
                spriteX: this.localPlayer.sprite?.x,
                spriteY: this.localPlayer.sprite?.y,
                spriteVisible: this.localPlayer.sprite?.visible
            });
            return;
        }

        // 如果没有任何玩家，创建一个
        console.log('🎭 没有找到玩家，创建新的测试玩家...');
        const testPlayerData = {
            id: 'force-test-player',
            name: '测试玩家',
            character: 'part_01',
            x: 200,
            y: 400,
            facing: 'right',
            health: 100
        };

        try {
            this.createOrUpdatePlayer(testPlayerData);
            this.localPlayer = this.players['force-test-player'];
            console.log('✅ 强制创建本地玩家成功:', this.localPlayer);
        } catch (error) {
            console.error('❌ 强制创建玩家失败:', error);
        }
    }

    createBackground() {
        // 创建渐变背景
        const graphics = this.add.graphics();
        graphics.fillGradientStyle(0x87CEEB, 0x87CEEB, 0x98FB98, 0x98FB98, 1);
        graphics.fillRect(0, 0, 800, 600);

        // 添加中心分界线
        const line = this.add.graphics();
        line.lineStyle(2, 0xffffff, 0.3);
        line.setLineDash([10, 10]);
        line.beginPath();
        line.moveTo(400, 0);
        line.lineTo(400, 600);
        line.strokePath();

        // 添加地面线
        const ground = this.add.graphics();
        ground.lineStyle(3, 0x8B4513);
        ground.beginPath();
        ground.moveTo(0, 520);
        ground.lineTo(800, 520);
        ground.strokePath();

        // 添加区域标识
        this.add.text(200, 30, '玩家1区域', {
            fontSize: '16px',
            fill: '#ffffff',
            alpha: 0.7
        }).setOrigin(0.5);

        this.add.text(600, 30, '玩家2区域', {
            fontSize: '16px',
            fill: '#ffffff',
            alpha: 0.7
        }).setOrigin(0.5);
    }

    createFallbackTextures() {
        // 创建一个简单的角色纹理作为备用
        const graphics = this.add.graphics();

        // 绘制一个简单的人形
        graphics.fillStyle(0x4CAF50); // 绿色
        graphics.fillRect(0, 0, 40, 60); // 身体

        graphics.fillStyle(0xFFDBB3); // 肤色
        graphics.fillCircle(20, 15, 12); // 头部

        graphics.fillStyle(0x2196F3); // 蓝色
        graphics.fillRect(5, 20, 30, 25); // 衣服

        graphics.fillStyle(0x795548); // 棕色
        graphics.fillRect(10, 45, 8, 15); // 左腿
        graphics.fillRect(22, 45, 8, 15); // 右腿

        // 生成纹理
        graphics.generateTexture('simple-character', 40, 60);
        graphics.destroy();

        console.log('备用角色纹理创建完成');
    }

    addTestSprites() {
        // 添加测试圆圈来验证渲染
        const testCircle = this.add.circle(100, 100, 20, 0xff0000);
        testCircle.setDepth(100);
        console.log('测试圆圈已添加');

        // 添加测试文字
        const testText = this.add.text(100, 150, '测试文字', {
            fontSize: '16px',
            fill: '#ffffff'
        });
        testText.setDepth(100);
        console.log('测试文字已添加');

        // 检查所有角色图片是否加载成功
        const characters = ['part_01', 'part_02', 'part_03', 'part_04', 'part_05', 'part_06',
                          'part_07', 'part_08', 'part_09', 'part_10', 'part_11', 'part_12'];

        let loadedCount = 0;
        characters.forEach((char, index) => {
            if (this.textures.exists(char)) {
                loadedCount++;
                console.log(`✓ ${char} 加载成功`);

                // 显示前3个角色作为测试
                if (index < 3) {
                    const testSprite = this.add.image(200 + index * 100, 200, char);
                    testSprite.setScale(1);
                    testSprite.setDepth(100);
                }
            } else {
                console.error(`✗ ${char} 加载失败`);
            }
        });

        console.log(`角色图片加载统计: ${loadedCount}/${characters.length}`);

        // 如果没有角色图片加载成功，显示警告
        if (loadedCount === 0) {
            const warningText = this.add.text(400, 300, '⚠️ 角色图片加载失败\n请检查assets目录', {
                fontSize: '20px',
                fill: '#ff0000',
                align: 'center'
            }).setOrigin(0.5);
            warningText.setDepth(100);
        }

        // 添加一个大的测试角色在中央
        const bigTestChar = this.add.circle(400, 300, 50, 0x00ff00);
        bigTestChar.setDepth(50);
        const testLabel = this.add.text(400, 300, '测试角色', {
            fontSize: '16px',
            fill: '#000000'
        }).setOrigin(0.5);
        testLabel.setDepth(51);
        console.log('大测试角色已添加到中央位置 (400, 300)');
    }

    showTestElements() {
        console.log('🔴 显示测试元素...');

        // 创建一个大的红色圆圈在中央
        const centerCircle = this.add.circle(400, 300, 80, 0xff0000);
        centerCircle.setDepth(100);

        // 添加文字标签
        const centerText = this.add.text(400, 300, '游戏场景\n正常工作!', {
            fontSize: '24px',
            fill: '#ffffff',
            align: 'center',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        centerText.setDepth(101);

        // 创建四个角落的小圆圈
        const corners = [
            {x: 50, y: 50, color: 0x00ff00},    // 左上绿色
            {x: 750, y: 50, color: 0x0000ff},   // 右上蓝色
            {x: 50, y: 550, color: 0xffff00},   // 左下黄色
            {x: 750, y: 550, color: 0xff00ff}   // 右下紫色
        ];

        corners.forEach((corner, index) => {
            const circle = this.add.circle(corner.x, corner.y, 30, corner.color);
            circle.setDepth(100);
            const text = this.add.text(corner.x, corner.y, `角落${index + 1}`, {
                fontSize: '14px',
                fill: '#000000'
            }).setOrigin(0.5);
            text.setDepth(101);
        });

        console.log('✅ 测试元素显示完成');
    }

    createTestPlayers() {
        console.log('🎭 创建测试玩家...');

        // 创建玩家1（左侧）
        const player1Data = {
            id: 'test-player-1',
            name: '玩家1',
            character: 'part_01',
            x: 200,
            y: 400,
            facing: 'right',
            health: 100
        };

        // 创建玩家2（右侧）
        const player2Data = {
            id: 'test-player-2',
            name: '玩家2',
            character: 'part_04',
            x: 600,
            y: 400,
            facing: 'left',
            health: 100
        };

        // 使用现有的创建玩家方法
        this.createOrUpdatePlayer(player1Data);
        this.createOrUpdatePlayer(player2Data);

        // 设置玩家1为本地可控制玩家
        this.localPlayer = this.players['test-player-1'];
        console.log('✅ 设置本地玩家为玩家1:', this.localPlayer);

        // 启动位置监控定时器
        this.startPositionMonitor();

        console.log('✅ 两个测试玩家创建完成');

        // 暂时禁用测试动画以避免闪烁
        // setTimeout(() => {
        //     this.addTestAnimations();
        // }, 1000);
    }

    addTestAnimations() {
        console.log('🎬 测试动画已禁用以避免闪烁');
        // 动画已禁用，角色将保持静止状态
    }

    startPositionMonitor() {
        // 每秒打印一次玩家位置（仅在移动时）
        let lastPos = { x: 0, y: 0 };

        setInterval(() => {
            if (this.localPlayer && this.localPlayer.sprite) {
                const currentPos = {
                    x: Math.round(this.localPlayer.sprite.x),
                    y: Math.round(this.localPlayer.sprite.y)
                };

                // 只在位置发生变化时打印
                if (currentPos.x !== lastPos.x || currentPos.y !== lastPos.y) {
                    console.log('📍 位置更新:', {
                        from: lastPos,
                        to: currentPos,
                        delta: {
                            x: currentPos.x - lastPos.x,
                            y: currentPos.y - lastPos.y
                        },
                        velocity: {
                            x: this.localPlayer.sprite.body.velocity.x,
                            y: this.localPlayer.sprite.body.velocity.y
                        }
                    });
                    lastPos = { ...currentPos };
                }
            }
        }, 100); // 每100ms检查一次
    }

    setupControls() {
        console.log('🎮 设置键盘控制...');

        // 创建方向键
        this.cursors = this.input.keyboard.createCursorKeys();

        // 创建WASD键
        this.wasd = this.input.keyboard.addKeys('W,S,A,D');

        // 创建攻击键
        this.attackKeys = this.input.keyboard.addKeys('J,K,L');

        // 创建技能键
        this.skillKeys = this.input.keyboard.addKeys('ONE');

        // 添加原始键盘事件监听器用于调试
        this.input.keyboard.on('keydown', (event) => {
            const key = event.key.toLowerCase();
            if (['w', 'a', 's', 'd', 'j', 'k', 'l', '1'].includes(key) ||
                ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
                console.log('⌨️  原始按键事件 - KeyDown:', {
                    key: event.key,
                    code: event.code,
                    timestamp: Date.now()
                });
            }
        });

        this.input.keyboard.on('keyup', (event) => {
            const key = event.key.toLowerCase();
            if (['w', 'a', 's', 'd', 'j', 'k', 'l', '1'].includes(key) ||
                ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
                console.log('⌨️  原始按键事件 - KeyUp:', {
                    key: event.key,
                    code: event.code,
                    timestamp: Date.now()
                });
            }
        });

        console.log('✅ 键盘控制设置完成');
        console.log('🔧 键盘对象:', {
            cursors: this.cursors,
            wasd: this.wasd,
            attackKeys: this.attackKeys,
            skillKeys: this.skillKeys
        });
    }

    initSocket() {
        // 检查是否有Socket.IO可用
        if (typeof io === 'undefined') {
            console.log('Socket.IO 不可用，使用离线模式');
            this.socket = {
                emit: () => {}, // 空函数，避免报错
                on: () => {},
                id: 'offline-player'
            };
            return;
        }

        try {
            // 这里会连接到现有的Socket.IO服务器
            this.socket = io();

            this.socket.on('connect', () => {
                console.log('Phaser客户端连接到服务器');
            });

            this.socket.on('room-update', (data) => {
                this.handleRoomUpdate(data);
            });

            this.socket.on('game-start', (data) => {
                this.handleGameStart(data);
            });

            this.socket.on('player-update', (data) => {
                this.handlePlayerUpdate(data);
            });

            this.socket.on('player-attack', (data) => {
                this.handlePlayerAttack(data);
            });

            this.socket.on('player-skill', (data) => {
                this.handlePlayerSkill(data);
            });

            this.socket.on('player-damage', (data) => {
                this.handlePlayerDamage(data);
            });

            this.socket.on('game-end', (data) => {
                this.handleGameEnd(data);
            });
        } catch (error) {
            console.log('Socket.IO 连接失败，使用离线模式:', error);
            this.socket = {
                emit: () => {}, // 空函数，避免报错
                on: () => {},
                id: 'offline-player'
            };
        }
    }

    initSoundManager() {
        // 使用Web Audio API创建音效管理器
        this.soundManager = new PhaserSoundManager();
    }

    update() {
        // 添加更详细的调试信息
        if (Math.random() < 0.01) { // 1%的概率打印，避免刷屏
            console.log('🔄 Update方法运行中...', {
                hasLocalPlayer: !!this.localPlayer,
                localPlayerId: this.localPlayer?.id,
                playersCount: Object.keys(this.players || {}).length,
                sceneActive: this.scene.isActive(),
                inputEnabled: !!this.input?.keyboard
            });
        }

        if (!this.localPlayer) {
            // 调试：打印本地玩家状态
            if (Math.random() < 0.01) { // 1%的概率打印，避免刷屏
                console.log('⚠️ 本地玩家未设置，当前玩家列表:', Object.keys(this.players));
            }
            return;
        }

        this.handleInput();
        this.updateSkillEffects();
    }

    handleInput() {
        if (!this.localPlayer || !this.localPlayer.sprite) {
            // 调试：每次都打印输入处理失败的原因
            console.log('⚠️ 输入处理失败:', {
                hasLocalPlayer: !!this.localPlayer,
                hasSprite: !!(this.localPlayer && this.localPlayer.sprite),
                localPlayer: this.localPlayer,
                playersCount: Object.keys(this.players || {}).length,
                playerIds: Object.keys(this.players || {})
            });
            return;
        }

        let moved = false;
        const speed = 200;
        let newVelocityX = 0;
        let newVelocityY = 0;
        let newFacing = this.localPlayer.facing;

        // 简化的键盘状态检查
        const keyStates = {
            W: this.wasd?.W?.isDown || false,
            A: this.wasd?.A?.isDown || false,
            S: this.wasd?.S?.isDown || false,
            D: this.wasd?.D?.isDown || false,
            UP: this.cursors?.up?.isDown || false,
            DOWN: this.cursors?.down?.isDown || false,
            LEFT: this.cursors?.left?.isDown || false,
            RIGHT: this.cursors?.right?.isDown || false
        };

        // 记录当前位置
        const currentPos = {
            x: this.localPlayer.sprite.x,
            y: this.localPlayer.sprite.y
        };

        // 检查是否有键被按下
        const anyKeyPressed = Object.values(keyStates).some(state => state);
        if (anyKeyPressed) {
            console.log('🎮 键盘状态:', keyStates);
            console.log('📍 当前位置:', currentPos);
        }

        // 简化的移动控制
        if (keyStates.A || keyStates.LEFT) {
            newVelocityX = -speed;
            newFacing = 'left';
            moved = true;
            console.log('⬅️ 向左移动');
        } else if (keyStates.D || keyStates.RIGHT) {
            newVelocityX = speed;
            newFacing = 'right';
            moved = true;
            console.log('➡️ 向右移动');
        }

        if (keyStates.W || keyStates.UP) {
            newVelocityY = -speed;
            moved = true;
            console.log('⬆️ 向上移动');
        } else if (keyStates.S || keyStates.DOWN) {
            newVelocityY = speed;
            moved = true;
            console.log('⬇️ 向下移动');
        }

        // 设置速度
        try {
            this.localPlayer.sprite.setVelocity(newVelocityX, newVelocityY);

            if (moved) {
                console.log('🏃 设置速度成功:', {
                    vx: newVelocityX,
                    vy: newVelocityY,
                    facing: newFacing
                });
            }
        } catch (error) {
            console.error('❌ 设置速度失败:', error);
        }

        // 更新面向方向
        if (newFacing !== this.localPlayer.facing) {
            this.localPlayer.facing = newFacing;
            try {
                this.localPlayer.sprite.setFlipX(newFacing === 'left');
                console.log('🔄 面向方向改变:', newFacing);
            } catch (error) {
                console.error('❌ 设置面向方向失败:', error);
            }
        }

        // 发送移动数据到服务器（仅在Socket可用时）
        if ((moved || newFacing !== this.localPlayer.facing) && this.socket && this.socket.connected) {
            this.socket.emit('player-move', {
                x: this.localPlayer.sprite.x,
                y: this.localPlayer.sprite.y,
                facing: newFacing,
                isMoving: moved
            });
        }

        // 攻击控制 - 增强版
        if (Phaser.Input.Keyboard.JustDown(this.attackKeys.J)) {
            console.log('🥊 拳击攻击！');
            this.performAttack('punch');
        } else if (Phaser.Input.Keyboard.JustDown(this.attackKeys.K)) {
            console.log('🦵 踢腿攻击！');
            this.performAttack('kick');
        } else if (Phaser.Input.Keyboard.JustDown(this.attackKeys.L)) {
            console.log('⚡ 特殊攻击！');
            this.performAttack('special');
        }

        // 技能控制 - 增强版
        if (Phaser.Input.Keyboard.JustDown(this.skillKeys.ONE)) {
            console.log('🎯 使用技能！');
            this.performSkill();
        }

        // 新增格斗按键
        this.handleAdvancedCombat();
    }

    handleAdvancedCombat() {
        // 初始化连击系统
        if (!this.comboSystem) {
            this.comboSystem = {
                lastAttackTime: 0,
                comboCount: 0,
                comboWindow: 1000, // 1秒连击窗口
                maxCombo: 5
            };
        }

        // 检查数字键技能 (2-9)
        for (let i = 2; i <= 9; i++) {
            const key = this.input.keyboard.addKey(`DIGIT${i}`);
            if (Phaser.Input.Keyboard.JustDown(key)) {
                console.log(`🎯 使用技能 ${i}！`);
                this.performSpecialSkill(i);
            }
        }

        // 组合键攻击
        this.handleComboAttacks();

        // 防御系统
        this.handleDefense();

        // 闪避系统
        this.handleDodge();
    }

    handleComboAttacks() {
        const currentTime = Date.now();

        // 重置连击计数器如果超时
        if (currentTime - this.comboSystem.lastAttackTime > this.comboSystem.comboWindow) {
            this.comboSystem.comboCount = 0;
        }

        // 检查连击组合
        if (this.wasd.W.isDown && Phaser.Input.Keyboard.JustDown(this.attackKeys.J)) {
            console.log('🚀 上勾拳连击！');
            this.performComboAttack('uppercut');
        } else if (this.wasd.S.isDown && Phaser.Input.Keyboard.JustDown(this.attackKeys.K)) {
            console.log('🌪️ 下扫腿连击！');
            this.performComboAttack('sweep');
        } else if ((this.wasd.A.isDown || this.wasd.D.isDown) && Phaser.Input.Keyboard.JustDown(this.attackKeys.L)) {
            console.log('⚡ 侧身特殊攻击！');
            this.performComboAttack('side_special');
        }
    }

    handleDefense() {
        // 空格键防御
        const spaceKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.SPACE);
        if (spaceKey.isDown) {
            if (!this.localPlayer.isDefending) {
                console.log('🛡️ 开始防御！');
                this.localPlayer.isDefending = true;
                this.showDefenseEffect();
            }
        } else if (this.localPlayer.isDefending) {
            console.log('🛡️ 停止防御！');
            this.localPlayer.isDefending = false;
            this.hideDefenseEffect();
        }
    }

    handleDodge() {
        // Shift + 方向键闪避
        const shiftKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.SHIFT);

        if (shiftKey.isDown && !this.localPlayer.isDodging) {
            let dodgeDirection = null;

            if (this.wasd.W.isDown || this.cursors.up.isDown) {
                dodgeDirection = 'up';
            } else if (this.wasd.S.isDown || this.cursors.down.isDown) {
                dodgeDirection = 'down';
            } else if (this.wasd.A.isDown || this.cursors.left.isDown) {
                dodgeDirection = 'left';
            } else if (this.wasd.D.isDown || this.cursors.right.isDown) {
                dodgeDirection = 'right';
            }

            if (dodgeDirection) {
                console.log(`💨 向${dodgeDirection}闪避！`);
                this.performDodge(dodgeDirection);
            }
        }
    }

    performAttack(attackType) {
        if (this.localPlayer.isAttacking) return;

        this.localPlayer.isAttacking = true;

        // 更新连击系统
        this.updateComboSystem();

        // 播放攻击音效
        this.soundManager?.playSound(attackType);

        // 播放攻击动画
        this.playAttackAnimation(this.localPlayer, attackType);

        // 发送攻击数据到服务器（仅在Socket可用时）
        if (this.socket && this.socket.connected) {
            this.socket.emit('player-attack', {
                attackType: attackType,
                x: this.localPlayer.sprite.x,
                y: this.localPlayer.sprite.y,
                comboCount: this.comboSystem.comboCount
            });
        }

        // 重置攻击状态
        setTimeout(() => {
            if (this.localPlayer) {
                this.localPlayer.isAttacking = false;
            }
        }, 500);
    }

    performComboAttack(comboType) {
        if (this.localPlayer.isAttacking) return;

        console.log(`🔥 连击攻击: ${comboType}, 连击数: ${this.comboSystem.comboCount + 1}`);

        this.localPlayer.isAttacking = true;
        this.updateComboSystem();

        // 连击伤害加成
        const damageMultiplier = 1 + (this.comboSystem.comboCount * 0.2);

        // 播放连击特效
        this.playComboAnimation(this.localPlayer, comboType, this.comboSystem.comboCount);

        // 发送连击数据到服务器
        if (this.socket && this.socket.connected) {
            this.socket.emit('player-combo-attack', {
                comboType: comboType,
                comboCount: this.comboSystem.comboCount,
                damageMultiplier: damageMultiplier,
                x: this.localPlayer.sprite.x,
                y: this.localPlayer.sprite.y
            });
        }

        // 重置攻击状态
        setTimeout(() => {
            if (this.localPlayer) {
                this.localPlayer.isAttacking = false;
            }
        }, 600);
    }

    performSpecialSkill(skillNumber) {
        if (this.localPlayer.isUsingSkill) return;

        this.localPlayer.isUsingSkill = true;

        const specialSkills = {
            2: { name: '火球术', effect: 'fireball', emoji: '🔥' },
            3: { name: '冰锥术', effect: 'icespike', emoji: '❄️' },
            4: { name: '雷电术', effect: 'lightning', emoji: '⚡' },
            5: { name: '治疗术', effect: 'heal', emoji: '💚' },
            6: { name: '护盾术', effect: 'shield', emoji: '🛡️' },
            7: { name: '瞬移术', effect: 'teleport', emoji: '💫' },
            8: { name: '狂暴术', effect: 'rage', emoji: '😡' },
            9: { name: '终极技', effect: 'ultimate', emoji: '💥' }
        };

        const skill = specialSkills[skillNumber];
        if (skill) {
            console.log(`${skill.emoji} 使用${skill.name}！`);
            this.createSpecialSkillEffect(skill.effect, this.localPlayer.sprite.x, this.localPlayer.sprite.y, this.localPlayer.facing);

            // 发送特殊技能数据到服务器
            if (this.socket && this.socket.connected) {
                this.socket.emit('player-special-skill', {
                    skillNumber: skillNumber,
                    skillType: skill.effect,
                    x: this.localPlayer.sprite.x,
                    y: this.localPlayer.sprite.y,
                    facing: this.localPlayer.facing
                });
            }
        }

        // 重置技能状态
        setTimeout(() => {
            if (this.localPlayer) {
                this.localPlayer.isUsingSkill = false;
            }
        }, 1500);
    }

    performDodge(direction) {
        if (this.localPlayer.isDodging) return;

        this.localPlayer.isDodging = true;

        // 闪避距离
        const dodgeDistance = 100;
        let newX = this.localPlayer.sprite.x;
        let newY = this.localPlayer.sprite.y;

        switch (direction) {
            case 'up':
                newY = Math.max(50, newY - dodgeDistance);
                break;
            case 'down':
                newY = Math.min(550, newY + dodgeDistance);
                break;
            case 'left':
                newX = Math.max(50, newX - dodgeDistance);
                break;
            case 'right':
                newX = Math.min(750, newX + dodgeDistance);
                break;
        }

        // 闪避动画
        this.tweens.add({
            targets: this.localPlayer.sprite,
            x: newX,
            y: newY,
            duration: 200,
            ease: 'Power2',
            onStart: () => {
                // 闪避时半透明
                this.localPlayer.sprite.setAlpha(0.5);
            },
            onComplete: () => {
                // 恢复不透明
                this.localPlayer.sprite.setAlpha(1);
                this.localPlayer.isDodging = false;
            }
        });

        // 发送闪避数据到服务器
        if (this.socket && this.socket.connected) {
            this.socket.emit('player-dodge', {
                direction: direction,
                x: newX,
                y: newY
            });
        }
    }

    updateComboSystem() {
        const currentTime = Date.now();

        // 如果在连击窗口内，增加连击数
        if (currentTime - this.comboSystem.lastAttackTime <= this.comboSystem.comboWindow) {
            this.comboSystem.comboCount = Math.min(this.comboSystem.comboCount + 1, this.comboSystem.maxCombo);
        } else {
            this.comboSystem.comboCount = 1;
        }

        this.comboSystem.lastAttackTime = currentTime;

        // 显示连击数
        if (this.comboSystem.comboCount > 1) {
            this.showComboCounter();
        }
    }

    showComboCounter() {
        // 移除之前的连击显示
        if (this.comboText) {
            this.comboText.destroy();
        }

        // 创建新的连击显示
        this.comboText = this.add.text(this.localPlayer.sprite.x, this.localPlayer.sprite.y - 120,
            `${this.comboSystem.comboCount} COMBO!`, {
            fontSize: '20px',
            fill: '#FFD700',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        // 动画效果
        this.tweens.add({
            targets: this.comboText,
            scale: 1.5,
            alpha: 0,
            y: this.comboText.y - 30,
            duration: 1000,
            onComplete: () => {
                if (this.comboText) {
                    this.comboText.destroy();
                    this.comboText = null;
                }
            }
        });
    }

    playComboAnimation(player, comboType, comboCount) {
        if (!player.sprite) return;

        const x = player.facing === 'right' ? player.sprite.x + 60 : player.sprite.x - 60;
        const y = player.sprite.y - 20;

        // 连击特效
        const comboEffects = {
            'uppercut': '🚀',
            'sweep': '🌪️',
            'side_special': '⚡'
        };

        const effectText = this.add.text(x, y, comboEffects[comboType] || '💥', {
            fontSize: `${24 + comboCount * 4}px`
        }).setOrigin(0.5);

        // 连击动画更华丽
        this.tweens.add({
            targets: effectText,
            alpha: 0,
            scale: 2 + comboCount * 0.3,
            rotation: Math.PI * 2,
            duration: 600,
            onComplete: () => {
                effectText.destroy();
            }
        });

        // 屏幕震动效果
        this.cameras.main.shake(100 + comboCount * 50, 0.01);
    }

    showDefenseEffect() {
        if (this.defenseShield) return;

        // 创建防御护盾效果
        this.defenseShield = this.add.circle(
            this.localPlayer.sprite.x,
            this.localPlayer.sprite.y,
            60,
            0x00BFFF,
            0.3
        );
        this.defenseShield.setStrokeStyle(3, 0x00BFFF, 0.8);
        this.defenseShield.setDepth(15);

        // 护盾脉动动画
        this.tweens.add({
            targets: this.defenseShield,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 500,
            yoyo: true,
            repeat: -1
        });
    }

    hideDefenseEffect() {
        if (this.defenseShield) {
            this.defenseShield.destroy();
            this.defenseShield = null;
        }
    }

    createSpecialSkillEffect(skillType, x, y, facing) {
        switch (skillType) {
            case 'fireball':
                this.createFireballEffect(x, y, facing);
                break;
            case 'icespike':
                this.createIceSpikeEffect(x, y, facing);
                break;
            case 'lightning':
                this.createLightningEffect(x, y);
                break;
            case 'heal':
                this.createHealEffect(x, y);
                break;
            case 'shield':
                this.createShieldEffect(x, y);
                break;
            case 'teleport':
                this.createTeleportEffect(x, y);
                break;
            case 'rage':
                this.createRageEffect(x, y);
                break;
            case 'ultimate':
                this.createUltimateEffect(x, y);
                break;
        }
    }

    createFireballEffect(x, y, facing) {
        const fireball = this.add.circle(x, y, 15, 0xFF4500);
        fireball.setStrokeStyle(3, 0xFF6600);

        const targetX = facing === 'right' ? x + 300 : x - 300;

        this.tweens.add({
            targets: fireball,
            x: targetX,
            duration: 1000,
            onComplete: () => {
                // 爆炸效果
                for (let i = 0; i < 8; i++) {
                    const spark = this.add.circle(fireball.x, fireball.y, 5, 0xFF4500);
                    this.tweens.add({
                        targets: spark,
                        x: spark.x + (Math.random() - 0.5) * 100,
                        y: spark.y + (Math.random() - 0.5) * 100,
                        alpha: 0,
                        duration: 500,
                        onComplete: () => spark.destroy()
                    });
                }
                fireball.destroy();
            }
        });
    }

    createIceSpikeEffect(x, y, facing) {
        const spikes = [];
        const direction = facing === 'right' ? 1 : -1;

        for (let i = 0; i < 5; i++) {
            const spike = this.add.triangle(
                x + (i * 40 * direction),
                y + 20,
                0, 0,
                10, 30,
                20, 0,
                0x87CEEB
            );
            spike.setStrokeStyle(2, 0x4682B4);
            spikes.push(spike);

            // 冰刺生长动画
            spike.setScale(0);
            this.tweens.add({
                targets: spike,
                scaleY: 1,
                duration: 200,
                delay: i * 100,
                onComplete: () => {
                    setTimeout(() => {
                        this.tweens.add({
                            targets: spike,
                            alpha: 0,
                            duration: 300,
                            onComplete: () => spike.destroy()
                        });
                    }, 1000);
                }
            });
        }
    }

    createLightningEffect(x, y) {
        // 创建闪电效果
        const lightning = this.add.graphics();
        lightning.lineStyle(4, 0xFFFF00, 1);

        // 绘制锯齿状闪电
        lightning.beginPath();
        lightning.moveTo(x, y - 200);
        for (let i = 0; i < 10; i++) {
            const nextX = x + (Math.random() - 0.5) * 40;
            const nextY = y - 200 + (i * 20);
            lightning.lineTo(nextX, nextY);
        }
        lightning.strokePath();

        // 闪电闪烁效果
        this.tweens.add({
            targets: lightning,
            alpha: 0,
            duration: 100,
            yoyo: true,
            repeat: 5,
            onComplete: () => lightning.destroy()
        });

        // 屏幕闪白效果
        const flash = this.add.rectangle(400, 300, 800, 600, 0xFFFFFF, 0.5);
        this.tweens.add({
            targets: flash,
            alpha: 0,
            duration: 200,
            onComplete: () => flash.destroy()
        });
    }

    createHealEffect(x, y) {
        // 治疗光环效果
        for (let i = 0; i < 6; i++) {
            const healParticle = this.add.circle(x, y, 8, 0x00FF00, 0.8);

            this.tweens.add({
                targets: healParticle,
                x: x + Math.cos(i * Math.PI / 3) * 50,
                y: y + Math.sin(i * Math.PI / 3) * 50,
                alpha: 0,
                duration: 1000,
                onComplete: () => healParticle.destroy()
            });
        }

        // 治疗数字显示
        const healText = this.add.text(x, y - 50, '+25', {
            fontSize: '20px',
            fill: '#00FF00',
            fontStyle: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: healText,
            y: healText.y - 30,
            alpha: 0,
            duration: 1000,
            onComplete: () => healText.destroy()
        });
    }

    createShieldEffect(x, y) {
        // 护盾效果
        const shield = this.add.circle(x, y, 60, 0x4169E1, 0.3);
        shield.setStrokeStyle(4, 0x4169E1, 0.8);

        // 护盾旋转效果
        this.tweens.add({
            targets: shield,
            rotation: Math.PI * 2,
            duration: 2000,
            repeat: 2,
            onComplete: () => {
                this.tweens.add({
                    targets: shield,
                    alpha: 0,
                    duration: 500,
                    onComplete: () => shield.destroy()
                });
            }
        });
    }

    createTeleportEffect(x, y) {
        // 瞬移特效
        const teleportCircle = this.add.circle(x, y, 80, 0x9400D3, 0.5);

        this.tweens.add({
            targets: teleportCircle,
            scaleX: 0,
            scaleY: 0,
            duration: 300,
            onComplete: () => teleportCircle.destroy()
        });

        // 随机瞬移位置
        const newX = 100 + Math.random() * 600;
        const newY = 100 + Math.random() * 400;

        // 瞬移玩家
        if (this.localPlayer && this.localPlayer.sprite) {
            this.localPlayer.sprite.setPosition(newX, newY);

            // 出现特效
            const appearCircle = this.add.circle(newX, newY, 80, 0x9400D3, 0.5);
            this.tweens.add({
                targets: appearCircle,
                scaleX: 0,
                scaleY: 0,
                duration: 300,
                onComplete: () => appearCircle.destroy()
            });
        }
    }

    createRageEffect(x, y) {
        // 狂暴效果 - 红色光环
        const rageAura = this.add.circle(x, y, 70, 0xFF0000, 0.3);
        rageAura.setStrokeStyle(5, 0xFF0000, 0.8);

        // 狂暴脉动效果
        this.tweens.add({
            targets: rageAura,
            scaleX: 1.5,
            scaleY: 1.5,
            duration: 200,
            yoyo: true,
            repeat: 10,
            onComplete: () => rageAura.destroy()
        });

        // 增加攻击力提示
        const rageText = this.add.text(x, y - 60, '攻击力 +50%!', {
            fontSize: '16px',
            fill: '#FF0000',
            fontStyle: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: rageText,
            y: rageText.y - 30,
            alpha: 0,
            duration: 2000,
            onComplete: () => rageText.destroy()
        });
    }

    createUltimateEffect(x, y) {
        // 终极技能 - 华丽的爆炸效果
        this.cameras.main.shake(500, 0.02);

        // 多层爆炸圆圈
        for (let i = 0; i < 5; i++) {
            const explosion = this.add.circle(x, y, 20 + i * 30, 0xFFD700, 0.8 - i * 0.15);
            explosion.setStrokeStyle(3, 0xFF4500);

            this.tweens.add({
                targets: explosion,
                scaleX: 3 + i,
                scaleY: 3 + i,
                alpha: 0,
                duration: 800 + i * 200,
                onComplete: () => explosion.destroy()
            });
        }

        // 终极技能文字
        const ultimateText = this.add.text(x, y - 100, '💥 ULTIMATE! 💥', {
            fontSize: '32px',
            fill: '#FFD700',
            fontStyle: 'bold',
            stroke: '#FF4500',
            strokeThickness: 3
        }).setOrigin(0.5);

        this.tweens.add({
            targets: ultimateText,
            scaleX: 1.5,
            scaleY: 1.5,
            alpha: 0,
            duration: 2000,
            onComplete: () => ultimateText.destroy()
        });
    }

    performSkill() {
        if (this.localPlayer.isUsingSkill) return;

        this.localPlayer.isUsingSkill = true;
        const skillType = this.getRandomSkill();
        
        // 播放技能音效
        this.soundManager?.playSound('skill');
        
        // 发送技能数据到服务器（仅在Socket可用时）
        if (this.socket && this.socket.connected) {
            this.socket.emit('player-skill', {
                skillType: skillType,
                x: this.localPlayer.sprite.x,
                y: this.localPlayer.sprite.y,
                facing: this.localPlayer.facing
            });
        }

        // 创建技能特效
        this.createSkillEffect(skillType, this.localPlayer.sprite.x, this.localPlayer.sprite.y, this.localPlayer.facing);

        // 重置技能状态
        setTimeout(() => {
            if (this.localPlayer) {
                this.localPlayer.isUsingSkill = false;
            }
        }, 1000);
    }

    getRandomSkill() {
        const skills = ['missile', 'fire', 'ice'];
        return skills[Math.floor(Math.random() * skills.length)];
    }

    // Socket事件处理方法
    handleRoomUpdate(data) {
        console.log('🔄 房间更新 - 玩家数量:', data.players.length);

        // 避免频繁重建 - 只在玩家数量变化时才重建
        const currentPlayerIds = Object.keys(this.players);
        const newPlayerIds = data.players.map(p => p.id);

        const playersChanged = currentPlayerIds.length !== newPlayerIds.length ||
                              !currentPlayerIds.every(id => newPlayerIds.includes(id));

        if (!playersChanged) {
            console.log('玩家列表未变化，跳过重建');
            return;
        }

        console.log('玩家列表变化，重建玩家对象');

        // 清理旧的玩家对象
        Object.values(this.players).forEach(player => {
            if (player.sprite) player.sprite.destroy();
            if (player.healthBarBg) player.healthBarBg.destroy();
            if (player.healthBar) player.healthBar.destroy();
            if (player.nameText) player.nameText.destroy();
        });
        this.players = {};

        data.players.forEach((player, index) => {
            console.log(`处理玩家 ${index + 1}:`, player);
            this.createOrUpdatePlayer(player);
            if (player.id === this.socket.id) {
                this.localPlayer = this.players[player.id];
                console.log('设置本地玩家:', this.localPlayer);
            }
        });

        console.log('✅ 房间更新完成，当前玩家对象:', Object.keys(this.players));
    }

    handleGameStart(data) {
        console.log('游戏开始:', data);
        this.soundManager?.playSound('gameStart');
    }

    handlePlayerUpdate(data) {
        if (this.players[data.playerId] && this.players[data.playerId].sprite) {
            const player = this.players[data.playerId];
            player.sprite.setPosition(data.playerState.x, data.playerState.y);
            player.facing = data.playerState.facing;
            player.sprite.setFlipX(data.playerState.facing === 'left');
        }
    }

    handlePlayerAttack(data) {
        if (this.players[data.playerId]) {
            this.playAttackAnimation(this.players[data.playerId], data.attackType);
        }
    }

    handlePlayerSkill(data) {
        if (this.players[data.playerId]) {
            this.createSkillEffect(data.skillType, data.x, data.y, data.facing);
        }
    }

    handlePlayerDamage(data) {
        if (this.players[data.playerId]) {
            this.showDamageEffect(this.players[data.playerId], data.damage);
            this.soundManager?.playSound('hit');
        }
    }

    handleGameEnd(data) {
        console.log('游戏结束:', data);
        const isWinner = data.winnerId === this.socket.id;
        this.soundManager?.playSound(isWinner ? 'victory' : 'defeat');

        // 显示游戏结束UI
        this.showGameEndScreen(data.winner, isWinner);
    }

    createTempCharacterTexture(playerId) {
        // 为每个玩家创建一个独特颜色的临时角色
        const colors = [0xFF5722, 0x2196F3, 0x4CAF50, 0xFF9800, 0x9C27B0];
        const color = colors[playerId.charCodeAt(0) % colors.length];

        const graphics = this.add.graphics();
        graphics.fillStyle(color);
        graphics.fillRect(0, 0, 40, 60);

        // 添加一个简单的脸
        graphics.fillStyle(0xFFFFFF);
        graphics.fillCircle(15, 20, 3); // 左眼
        graphics.fillCircle(25, 20, 3); // 右眼
        graphics.fillRect(18, 25, 4, 2); // 嘴巴

        graphics.generateTexture(`temp-char-${playerId}`, 40, 60);
        graphics.destroy();

        console.log(`为玩家 ${playerId} 创建临时角色纹理`);
    }

    createOrUpdatePlayer(playerData) {
        console.log('创建/更新玩家:', playerData);

        if (!this.players[playerData.id]) {
            console.log(`创建新玩家: ${playerData.name}, 角色: ${playerData.character}, 位置: (${playerData.x}, ${playerData.y})`);

            // 检查角色图片是否存在
            let characterTexture = playerData.character;
            if (!this.textures.exists(characterTexture)) {
                console.error(`角色图片不存在: ${characterTexture}`);
                // 尝试使用part_01作为备用
                if (this.textures.exists('part_01')) {
                    characterTexture = 'part_01';
                    console.log('使用part_01作为备用角色');
                } else if (this.textures.exists('simple-character')) {
                    // 使用简单角色图片
                    characterTexture = 'simple-character';
                    console.log('使用简单角色图片');
                } else {
                    // 最后的备用方案：创建一个临时的彩色方块
                    console.warn('所有角色图片都不可用，创建临时角色');
                    this.createTempCharacterTexture(playerData.id);
                    characterTexture = `temp-char-${playerData.id}`;
                }
            }

            try {
                // 创建新玩家精灵
                const sprite = this.physics.add.sprite(playerData.x, playerData.y, characterTexture);
                sprite.setScale(0.2); // 调整角色大小为合适比例（缩小4倍）
                sprite.setCollideWorldBounds(true);
                sprite.setFlipX(playerData.facing === 'left');
                sprite.setDepth(10); // 确保角色在前景

                console.log(`玩家精灵创建成功: ${playerData.name}`);

                // 创建血条背景
                const healthBarBg = this.add.rectangle(playerData.x, playerData.y - 80, 80, 8, 0x000000, 0.5);
                healthBarBg.setDepth(20);

                // 创建血条
                const healthBar = this.add.rectangle(playerData.x, playerData.y - 80, 80, 8, 0x4CAF50);
                healthBar.setDepth(21);

                // 创建玩家名字
                const nameText = this.add.text(playerData.x, playerData.y - 100, playerData.name, {
                    fontSize: '14px',
                    fill: '#ffffff',
                    stroke: '#000000',
                    strokeThickness: 2
                }).setOrigin(0.5);
                nameText.setDepth(22);

                this.players[playerData.id] = {
                    ...playerData,
                    sprite: sprite,
                    healthBarBg: healthBarBg,
                    healthBar: healthBar,
                    nameText: nameText,
                    health: playerData.health || 100, // 确保血量正确设置
                    facing: playerData.facing || 'right',
                    isAttacking: false,
                    isDefending: false,
                    isUsingSkill: false,
                    isDodging: false
                };

                console.log(`玩家 ${playerData.name} 创建完成，精灵可见性:`, sprite.visible);
                console.log(`精灵位置: (${sprite.x}, ${sprite.y}), 缩放: ${sprite.scale}, 深度: ${sprite.depth}`);
                console.log(`玩家血量: ${this.players[playerData.id].health}, 血条已创建: ${!!healthBar}`);

            } catch (error) {
                console.error('创建玩家精灵时出错:', error);
            }
        } else {
            // 更新现有玩家
            const player = this.players[playerData.id];
            Object.assign(player, playerData);

            if (player.sprite) {
                player.sprite.setPosition(playerData.x, playerData.y);
                player.sprite.setFlipX(playerData.facing === 'left');

                // 更新血条和名字位置
                if (player.healthBarBg) player.healthBarBg.setPosition(playerData.x, playerData.y - 80);
                if (player.healthBar) player.healthBar.setPosition(playerData.x, playerData.y - 80);
                if (player.nameText) player.nameText.setPosition(playerData.x, playerData.y - 100);
            }
        }
    }

    playAttackAnimation(player, attackType) {
        if (!player.sprite) return;

        // 创建攻击特效
        const x = player.facing === 'right' ? player.sprite.x + 60 : player.sprite.x - 60;
        const y = player.sprite.y - 20;

        // 根据攻击类型显示不同特效
        const effectEmoji = {
            'punch': '👊',
            'kick': '🦵',
            'special': '⚡'
        };

        const effectText = this.add.text(x, y, effectEmoji[attackType] || '💥', {
            fontSize: '24px'
        }).setOrigin(0.5);

        // 动画效果
        this.tweens.add({
            targets: effectText,
            alpha: 0,
            scale: 2,
            duration: 500,
            onComplete: () => {
                effectText.destroy();
            }
        });

        // 暂时禁用玩家闪烁效果以避免持续闪烁
        // this.tweens.add({
        //     targets: player.sprite,
        //     alpha: 0.5,
        //     duration: 100,
        //     yoyo: true,
        //     repeat: 2
        // });
    }

    createSkillEffect(skillType, x, y, facing) {
        switch (skillType) {
            case 'missile':
                this.createMissileEffect(x, y, facing);
                break;
            case 'fire':
                this.createFireEffect(x, y, facing);
                break;
            case 'ice':
                this.createIceEffect(x, y, facing);
                break;
        }
    }

    createMissileEffect(x, y, facing) {
        const missile = this.add.image(x, y - 50, 'skill-01');
        missile.setScale(0.5);
        missile.setFlipX(facing === 'left');

        const targetX = facing === 'right' ? x + 400 : x - 400;

        this.tweens.add({
            targets: missile,
            x: targetX,
            duration: 2000,
            onComplete: () => {
                // 爆炸效果
                const explosion = this.add.circle(missile.x, missile.y, 30, 0xff6600, 0.8);
                this.tweens.add({
                    targets: explosion,
                    scale: 2,
                    alpha: 0,
                    duration: 300,
                    onComplete: () => {
                        explosion.destroy();
                    }
                });
                missile.destroy();
            }
        });
    }

    createFireEffect(x, y, facing) {
        const fireX = facing === 'right' ? x + 80 : x - 80;
        const fire = this.add.image(fireX, y - 60, 'skill-02');
        fire.setScale(0.8);

        // 火焰跳动动画
        this.tweens.add({
            targets: fire,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 200,
            yoyo: true,
            repeat: 7,
            onComplete: () => {
                fire.destroy();
            }
        });

        // 添加粒子效果
        this.createFireParticles(fireX, y - 60);
    }

    createIceEffect(x, y, facing) {
        const iceX = facing === 'right' ? x + 100 : x - 100;
        const ice = this.add.image(iceX, y - 40, 'skill-03');
        ice.setScale(0.6);

        // 冰块生长动画
        this.tweens.add({
            targets: ice,
            scale: 1.2,
            duration: 2500,
            onComplete: () => {
                // 冰块破碎效果
                this.tweens.add({
                    targets: ice,
                    alpha: 0,
                    angle: 45,
                    duration: 300,
                    onComplete: () => {
                        ice.destroy();
                    }
                });
            }
        });

        // 冰晶环绕效果
        const circle = this.add.circle(iceX, y - 40, 30);
        circle.setStrokeStyle(2, 0xADD8E6, 0.8);
        this.tweens.add({
            targets: circle,
            scaleX: 1.5,
            scaleY: 1.5,
            alpha: 0,
            duration: 2500,
            onComplete: () => {
                circle.destroy();
            }
        });
    }

    createFireParticles(x, y) {
        // 简单的火花效果
        for (let i = 0; i < 5; i++) {
            const spark = this.add.circle(
                x + (Math.random() - 0.5) * 60,
                y + (Math.random() - 0.5) * 60,
                2,
                0xff6600
            );

            this.tweens.add({
                targets: spark,
                alpha: 0,
                y: spark.y - 20,
                duration: 1000,
                delay: i * 100,
                onComplete: () => {
                    spark.destroy();
                }
            });
        }
    }

    showDamageEffect(player, damage) {
        if (!player.sprite) return;

        // 显示伤害数字
        const damageText = this.add.text(player.sprite.x, player.sprite.y - 50, `-${damage}`, {
            fontSize: '20px',
            fill: '#ff0000',
            fontStyle: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: damageText,
            y: damageText.y - 30,
            alpha: 0,
            duration: 1000,
            onComplete: () => {
                damageText.destroy();
            }
        });

        // 更新血条
        const healthPercent = player.health / 100;
        this.tweens.add({
            targets: player.healthBar,
            scaleX: healthPercent,
            duration: 300
        });

        // 改变血条颜色
        const color = healthPercent > 0.5 ? 0x4CAF50 :
                     healthPercent > 0.25 ? 0xFF9800 : 0xF44336;
        player.healthBar.setFillStyle(color);
    }

    showGameEndScreen(winner, isWinner) {
        // 创建游戏结束覆盖层
        const overlay = this.add.rectangle(400, 300, 800, 600, 0x000000, 0.7);

        const resultText = this.add.text(400, 250, isWinner ? '🎉 你赢了！' : '😢 你输了！', {
            fontSize: '48px',
            fill: isWinner ? '#4CAF50' : '#F44336',
            fontStyle: 'bold'
        }).setOrigin(0.5);

        const winnerText = this.add.text(400, 320, `获胜者: ${winner}`, {
            fontSize: '24px',
            fill: '#ffffff'
        }).setOrigin(0.5);

        const restartText = this.add.text(400, 380, '点击任意键重新开始', {
            fontSize: '18px',
            fill: '#ffffff',
            alpha: 0.8
        }).setOrigin(0.5);

        // 闪烁效果
        this.tweens.add({
            targets: restartText,
            alpha: 0.3,
            duration: 1000,
            yoyo: true,
            repeat: -1
        });

        // 监听重新开始
        this.input.keyboard.once('keydown', () => {
            // 重新加载页面或重置游戏
            window.location.reload();
        });
    }

    updateSkillEffects() {
        // 更新技能效果（如果需要持续更新的话）
    }

    // 公共方法，供外部调用
    joinGame(playerData) {
        this.socket.emit('join-game', playerData);
    }
}

// 将GameScene暴露到全局作用域
window.GameScene = GameScene;

// 音效管理器类
class PhaserSoundManager {
    constructor() {
        this.audioContext = null;
        this.isMuted = false;
        this.effectVolume = 0.5;
        this.initAudio();
    }

    initAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.warn('Web Audio API not supported');
        }
    }

    playSound(type) {
        if (!this.audioContext || this.isMuted) return;

        switch (type) {
            case 'punch':
                this.createPunchSound();
                break;
            case 'kick':
                this.createKickSound();
                break;
            case 'special':
                this.createSpecialSound();
                break;
            case 'skill':
                this.createSkillSound();
                break;
            case 'hit':
                this.createHitSound();
                break;
        }
    }

    createPunchSound() {
        // 简化的音效创建
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(100, this.audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(this.effectVolume, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.1);
    }

    // 其他音效方法...
    createKickSound() { /* 实现踢腿音效 */ }
    createSpecialSound() { /* 实现特殊技能音效 */ }
    createSkillSound() { /* 实现技能音效 */ }
    createHitSound() { /* 实现受伤音效 */ }
}

// 将PhaserSoundManager也暴露到全局作用域
window.PhaserSoundManager = PhaserSoundManager;
