// Phaser.js版本的意大利脑残山海经格斗游戏

class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });
        this.socket = null;
        this.players = {};
        this.localPlayer = null;
        this.cursors = null;
        this.attackKeys = null;
        this.skillKeys = null;
        this.activeSkills = [];
        this.soundManager = null;
    }

    preload() {
        console.log('开始加载游戏资源...');

        // 加载角色图片
        const characters = ['part_01', 'part_02', 'part_03', 'part_04', 'part_05', 'part_06',
                          'part_07', 'part_08', 'part_09', 'part_10', 'part_11', 'part_12'];

        characters.forEach(char => {
            this.load.image(char, `./assets/${char}.png`);
            console.log(`加载角色图片: ${char} from ./assets/${char}.png`);
        });

        // 加载技能图片（如果存在的话）
        this.load.image('skill-01', './assets/skill-01.png');
        this.load.image('skill-02', './assets/skill-02.png');
        this.load.image('skill-03', './assets/skill-03.png');

        // 创建简单的像素图形作为占位符
        this.load.image('ground', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');

        // 创建备用角色图片（简单的彩色方块）
        this.load.image('fallback-player', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mNk+M9QjxdgGBUYNYBhDQA+/AH/0MQgNwAAAABJRU5ErkJggg==');

        // 添加加载事件监听
        this.load.on('filecomplete', (key, type, data) => {
            console.log(`资源加载完成: ${key} (${type})`);
        });

        this.load.on('loaderror', (file) => {
            console.error(`资源加载失败: ${file.key} - ${file.src}`);
        });

        this.load.on('complete', () => {
            console.log('所有资源加载完成');
            this.testImageUrls();
        });
    }

    testImageUrls() {
        // 测试图片URL是否可访问
        const testImg = new Image();
        testImg.onload = () => {
            console.log('✓ 图片URL测试成功: ./assets/part_01.png');
        };
        testImg.onerror = () => {
            console.error('✗ 图片URL测试失败: ./assets/part_01.png');
            console.log('尝试其他路径...');

            // 尝试不同的路径
            const testImg2 = new Image();
            testImg2.onload = () => console.log('✓ 备用路径成功: assets/part_01.png');
            testImg2.onerror = () => console.error('✗ 备用路径也失败: assets/part_01.png');
            testImg2.src = 'assets/part_01.png';
        };
        testImg.src = './assets/part_01.png';
    }

    create() {
        console.log('🎮 开始创建Phaser游戏场景...');
        console.log('🔧 场景状态:', {
            key: this.scene.key,
            isActive: this.scene.isActive(),
            isVisible: this.scene.isVisible()
        });

        // 设置世界边界
        this.physics.world.setBounds(0, 0, 800, 600);
        console.log('✅ 世界边界设置完成');

        // 创建背景
        this.createBackground();
        console.log('背景创建完成');

        // 设置输入控制
        this.setupControls();
        console.log('输入控制设置完成');

        // 初始化Socket连接
        this.initSocket();
        console.log('Socket连接初始化完成');

        // 初始化音效
        this.initSoundManager();
        console.log('音效管理器初始化完成');

        // 创建备用角色纹理
        this.createFallbackTextures();

        // 禁用测试精灵以避免背景闪烁
        // this.addTestSprites();

        console.log('🎮 Phaser游戏场景创建完成');
        console.log('场景对象:', this);
        console.log('物理世界:', this.physics.world);

        // 禁用测试元素以避免背景闪烁
        // this.showTestElements();

        // 不自动创建测试玩家，等待phaser-main.js创建
        // this.createTestPlayers();

        console.log('🎉 GameScene.create() 方法执行完成！');
        console.log('🎮 游戏场景已完全初始化');

        // 添加基础键盘测试
        this.addBasicKeyboardTest();
    }

    addBasicKeyboardTest() {
        console.log('🔧 添加基础键盘测试...');

        // 检查Phaser输入系统状态
        console.log('🔍 Phaser输入系统状态:', {
            hasInput: !!this.input,
            hasKeyboard: !!this.input?.keyboard,
            sceneActive: this.scene.isActive(),
            sceneVisible: this.scene.isVisible()
        });

        if (!this.input || !this.input.keyboard) {
            console.error('❌ Phaser键盘输入系统不可用');
            return;
        }

        // 添加全局键盘事件监听器
        document.addEventListener('keydown', (event) => {
            console.log('🌍 场景内全局键盘事件 - KeyDown:', {
                key: event.key,
                code: event.code,
                timestamp: Date.now(),
                target: event.target.tagName
            });
        });

        document.addEventListener('keyup', (event) => {
            console.log('🌍 场景内全局键盘事件 - KeyUp:', {
                key: event.key,
                code: event.code,
                timestamp: Date.now(),
                target: event.target.tagName
            });
        });

        // 添加Phaser特定的键盘事件监听器
        try {
            this.input.keyboard.on('keydown', (event) => {
                console.log('🎮 Phaser键盘事件 - KeyDown:', {
                    key: event.key,
                    code: event.code,
                    timestamp: Date.now()
                });
            });

            this.input.keyboard.on('keyup', (event) => {
                console.log('🎮 Phaser键盘事件 - KeyUp:', {
                    key: event.key,
                    code: event.code,
                    timestamp: Date.now()
                });
            });

            console.log('✅ Phaser键盘事件监听器设置成功');
        } catch (error) {
            console.error('❌ 设置Phaser键盘事件监听器失败:', error);
        }

        console.log('✅ 基础键盘测试设置完成');
        console.log('💡 请按任意键测试键盘事件是否正常工作');

        // 强制测试键盘系统
        setTimeout(() => {
            console.log('🧪 强制测试键盘系统...');
            console.log('🔍 当前键盘对象状态:', {
                cursors: !!this.cursors,
                wasd: !!this.wasd,
                attackKeys: !!this.attackKeys,
                skillKeys: !!this.skillKeys
            });
        }, 1000);
    }

    createBackground() {
        // 创建渐变背景
        const graphics = this.add.graphics();
        graphics.fillGradientStyle(0x87CEEB, 0x87CEEB, 0x98FB98, 0x98FB98, 1);
        graphics.fillRect(0, 0, 800, 600);

        // 添加中心分界线
        const line = this.add.graphics();
        line.lineStyle(2, 0xffffff, 0.3);
        line.setLineDash([10, 10]);
        line.beginPath();
        line.moveTo(400, 0);
        line.lineTo(400, 600);
        line.strokePath();

        // 添加地面线
        const ground = this.add.graphics();
        ground.lineStyle(3, 0x8B4513);
        ground.beginPath();
        ground.moveTo(0, 520);
        ground.lineTo(800, 520);
        ground.strokePath();

        // 添加区域标识
        this.add.text(200, 30, '玩家1区域', {
            fontSize: '16px',
            fill: '#ffffff',
            alpha: 0.7
        }).setOrigin(0.5);

        this.add.text(600, 30, '玩家2区域', {
            fontSize: '16px',
            fill: '#ffffff',
            alpha: 0.7
        }).setOrigin(0.5);
    }

    createFallbackTextures() {
        // 创建一个简单的角色纹理作为备用
        const graphics = this.add.graphics();

        // 绘制一个简单的人形
        graphics.fillStyle(0x4CAF50); // 绿色
        graphics.fillRect(0, 0, 40, 60); // 身体

        graphics.fillStyle(0xFFDBB3); // 肤色
        graphics.fillCircle(20, 15, 12); // 头部

        graphics.fillStyle(0x2196F3); // 蓝色
        graphics.fillRect(5, 20, 30, 25); // 衣服

        graphics.fillStyle(0x795548); // 棕色
        graphics.fillRect(10, 45, 8, 15); // 左腿
        graphics.fillRect(22, 45, 8, 15); // 右腿

        // 生成纹理
        graphics.generateTexture('simple-character', 40, 60);
        graphics.destroy();

        console.log('备用角色纹理创建完成');
    }

    addTestSprites() {
        // 添加测试圆圈来验证渲染
        const testCircle = this.add.circle(100, 100, 20, 0xff0000);
        testCircle.setDepth(100);
        console.log('测试圆圈已添加');

        // 添加测试文字
        const testText = this.add.text(100, 150, '测试文字', {
            fontSize: '16px',
            fill: '#ffffff'
        });
        testText.setDepth(100);
        console.log('测试文字已添加');

        // 检查所有角色图片是否加载成功
        const characters = ['part_01', 'part_02', 'part_03', 'part_04', 'part_05', 'part_06',
                          'part_07', 'part_08', 'part_09', 'part_10', 'part_11', 'part_12'];

        let loadedCount = 0;
        characters.forEach((char, index) => {
            if (this.textures.exists(char)) {
                loadedCount++;
                console.log(`✓ ${char} 加载成功`);

                // 显示前3个角色作为测试
                if (index < 3) {
                    const testSprite = this.add.image(200 + index * 100, 200, char);
                    testSprite.setScale(1);
                    testSprite.setDepth(100);
                }
            } else {
                console.error(`✗ ${char} 加载失败`);
            }
        });

        console.log(`角色图片加载统计: ${loadedCount}/${characters.length}`);

        // 如果没有角色图片加载成功，显示警告
        if (loadedCount === 0) {
            const warningText = this.add.text(400, 300, '⚠️ 角色图片加载失败\n请检查assets目录', {
                fontSize: '20px',
                fill: '#ff0000',
                align: 'center'
            }).setOrigin(0.5);
            warningText.setDepth(100);
        }

        // 添加一个大的测试角色在中央
        const bigTestChar = this.add.circle(400, 300, 50, 0x00ff00);
        bigTestChar.setDepth(50);
        const testLabel = this.add.text(400, 300, '测试角色', {
            fontSize: '16px',
            fill: '#000000'
        }).setOrigin(0.5);
        testLabel.setDepth(51);
        console.log('大测试角色已添加到中央位置 (400, 300)');
    }

    showTestElements() {
        console.log('🔴 显示测试元素...');

        // 创建一个大的红色圆圈在中央
        const centerCircle = this.add.circle(400, 300, 80, 0xff0000);
        centerCircle.setDepth(100);

        // 添加文字标签
        const centerText = this.add.text(400, 300, '游戏场景\n正常工作!', {
            fontSize: '24px',
            fill: '#ffffff',
            align: 'center',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        centerText.setDepth(101);

        // 创建四个角落的小圆圈
        const corners = [
            {x: 50, y: 50, color: 0x00ff00},    // 左上绿色
            {x: 750, y: 50, color: 0x0000ff},   // 右上蓝色
            {x: 50, y: 550, color: 0xffff00},   // 左下黄色
            {x: 750, y: 550, color: 0xff00ff}   // 右下紫色
        ];

        corners.forEach((corner, index) => {
            const circle = this.add.circle(corner.x, corner.y, 30, corner.color);
            circle.setDepth(100);
            const text = this.add.text(corner.x, corner.y, `角落${index + 1}`, {
                fontSize: '14px',
                fill: '#000000'
            }).setOrigin(0.5);
            text.setDepth(101);
        });

        console.log('✅ 测试元素显示完成');
    }

    createTestPlayers() {
        console.log('🎭 创建测试玩家...');

        // 创建玩家1（左侧）
        const player1Data = {
            id: 'test-player-1',
            name: '玩家1',
            character: 'part_01',
            x: 200,
            y: 400,
            facing: 'right',
            health: 100
        };

        // 创建玩家2（右侧）
        const player2Data = {
            id: 'test-player-2',
            name: '玩家2',
            character: 'part_04',
            x: 600,
            y: 400,
            facing: 'left',
            health: 100
        };

        // 使用现有的创建玩家方法
        this.createOrUpdatePlayer(player1Data);
        this.createOrUpdatePlayer(player2Data);

        // 设置玩家1为本地可控制玩家
        this.localPlayer = this.players['test-player-1'];
        console.log('✅ 设置本地玩家为玩家1:', this.localPlayer);

        // 启动位置监控定时器
        this.startPositionMonitor();

        console.log('✅ 两个测试玩家创建完成');

        // 暂时禁用测试动画以避免闪烁
        // setTimeout(() => {
        //     this.addTestAnimations();
        // }, 1000);
    }

    addTestAnimations() {
        console.log('🎬 测试动画已禁用以避免闪烁');
        // 动画已禁用，角色将保持静止状态
    }

    startPositionMonitor() {
        // 每秒打印一次玩家位置（仅在移动时）
        let lastPos = { x: 0, y: 0 };

        setInterval(() => {
            if (this.localPlayer && this.localPlayer.sprite) {
                const currentPos = {
                    x: Math.round(this.localPlayer.sprite.x),
                    y: Math.round(this.localPlayer.sprite.y)
                };

                // 只在位置发生变化时打印
                if (currentPos.x !== lastPos.x || currentPos.y !== lastPos.y) {
                    console.log('📍 位置更新:', {
                        from: lastPos,
                        to: currentPos,
                        delta: {
                            x: currentPos.x - lastPos.x,
                            y: currentPos.y - lastPos.y
                        },
                        velocity: {
                            x: this.localPlayer.sprite.body.velocity.x,
                            y: this.localPlayer.sprite.body.velocity.y
                        }
                    });
                    lastPos = { ...currentPos };
                }
            }
        }, 100); // 每100ms检查一次
    }

    setupControls() {
        console.log('🎮 设置键盘控制...');

        // 创建方向键
        this.cursors = this.input.keyboard.createCursorKeys();

        // 创建WASD键
        this.wasd = this.input.keyboard.addKeys('W,S,A,D');

        // 创建攻击键
        this.attackKeys = this.input.keyboard.addKeys('J,K,L');

        // 创建技能键
        this.skillKeys = this.input.keyboard.addKeys('ONE');

        // 添加原始键盘事件监听器用于调试
        this.input.keyboard.on('keydown', (event) => {
            const key = event.key.toLowerCase();
            if (['w', 'a', 's', 'd', 'j', 'k', 'l', '1'].includes(key) ||
                ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
                console.log('⌨️  原始按键事件 - KeyDown:', {
                    key: event.key,
                    code: event.code,
                    timestamp: Date.now()
                });
            }
        });

        this.input.keyboard.on('keyup', (event) => {
            const key = event.key.toLowerCase();
            if (['w', 'a', 's', 'd', 'j', 'k', 'l', '1'].includes(key) ||
                ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
                console.log('⌨️  原始按键事件 - KeyUp:', {
                    key: event.key,
                    code: event.code,
                    timestamp: Date.now()
                });
            }
        });

        console.log('✅ 键盘控制设置完成');
        console.log('🔧 键盘对象:', {
            cursors: this.cursors,
            wasd: this.wasd,
            attackKeys: this.attackKeys,
            skillKeys: this.skillKeys
        });
    }

    initSocket() {
        // 检查是否有Socket.IO可用
        if (typeof io === 'undefined') {
            console.log('Socket.IO 不可用，使用离线模式');
            this.socket = {
                emit: () => {}, // 空函数，避免报错
                on: () => {},
                id: 'offline-player'
            };
            return;
        }

        try {
            // 这里会连接到现有的Socket.IO服务器
            this.socket = io();

            this.socket.on('connect', () => {
                console.log('Phaser客户端连接到服务器');
            });

            this.socket.on('room-update', (data) => {
                this.handleRoomUpdate(data);
            });

            this.socket.on('game-start', (data) => {
                this.handleGameStart(data);
            });

            this.socket.on('player-update', (data) => {
                this.handlePlayerUpdate(data);
            });

            this.socket.on('player-attack', (data) => {
                this.handlePlayerAttack(data);
            });

            this.socket.on('player-skill', (data) => {
                this.handlePlayerSkill(data);
            });

            this.socket.on('player-damage', (data) => {
                this.handlePlayerDamage(data);
            });

            this.socket.on('game-end', (data) => {
                this.handleGameEnd(data);
            });
        } catch (error) {
            console.log('Socket.IO 连接失败，使用离线模式:', error);
            this.socket = {
                emit: () => {}, // 空函数，避免报错
                on: () => {},
                id: 'offline-player'
            };
        }
    }

    initSoundManager() {
        // 使用Web Audio API创建音效管理器
        this.soundManager = new PhaserSoundManager();
    }

    update() {
        if (!this.localPlayer) {
            // 调试：打印本地玩家状态
            if (Math.random() < 0.01) { // 1%的概率打印，避免刷屏
                console.log('⚠️ 本地玩家未设置，当前玩家列表:', Object.keys(this.players));
                console.log('🔍 游戏状态检查:', {
                    hasLocalPlayer: !!this.localPlayer,
                    playersCount: Object.keys(this.players).length,
                    sceneActive: this.scene.isActive(),
                    inputEnabled: !!this.input.keyboard
                });
            }
            return;
        }

        this.handleInput();
        this.updateSkillEffects();
    }

    handleInput() {
        if (!this.localPlayer || !this.localPlayer.sprite) {
            // 调试：打印输入处理失败的原因
            if (Math.random() < 0.01) { // 1%的概率打印
                console.log('⚠️ 输入处理失败:', {
                    hasLocalPlayer: !!this.localPlayer,
                    hasSprite: !!(this.localPlayer && this.localPlayer.sprite),
                    localPlayer: this.localPlayer
                });
            }
            return;
        }

        let moved = false;
        const speed = 200;
        let newVelocityX = 0;
        let newVelocityY = 0;
        let newFacing = this.localPlayer.facing;

        // 调试：检查键盘状态和玩家位置
        const keyStates = {
            W: this.wasd.W.isDown,
            A: this.wasd.A.isDown,
            S: this.wasd.S.isDown,
            D: this.wasd.D.isDown,
            UP: this.cursors.up.isDown,
            DOWN: this.cursors.down.isDown,
            LEFT: this.cursors.left.isDown,
            RIGHT: this.cursors.right.isDown
        };

        // 记录当前位置
        const currentPos = {
            x: this.localPlayer.sprite.x,
            y: this.localPlayer.sprite.y
        };

        // 只在有键被按下时才打印日志
        const anyKeyPressed = Object.values(keyStates).some(state => state);
        if (anyKeyPressed) {
            console.log('🎮 键盘状态:', keyStates);
            console.log('📍 当前位置:', currentPos);
        }

        // 移动控制
        if (this.wasd.A.isDown || this.cursors.left.isDown) {
            newVelocityX = -speed;
            newFacing = 'left';
            moved = true;
        } else if (this.wasd.D.isDown || this.cursors.right.isDown) {
            newVelocityX = speed;
            newFacing = 'right';
            moved = true;
        }

        if (this.wasd.W.isDown || this.cursors.up.isDown) {
            newVelocityY = -speed;
            moved = true;
        } else if (this.wasd.S.isDown || this.cursors.down.isDown) {
            newVelocityY = speed;
            moved = true;
        }

        // 设置速度
        this.localPlayer.sprite.setVelocity(newVelocityX, newVelocityY);

        // 调试：打印速度变化
        if (moved) {
            console.log('🏃 设置速度:', {
                vx: newVelocityX,
                vy: newVelocityY,
                facing: newFacing,
                oldPos: currentPos,
                newPos: { x: this.localPlayer.sprite.x, y: this.localPlayer.sprite.y }
            });
        }

        // 更新面向方向
        if (newFacing !== this.localPlayer.facing) {
            this.localPlayer.facing = newFacing;
            this.localPlayer.sprite.setFlipX(newFacing === 'left');
            console.log('🔄 面向方向改变:', newFacing);
        }

        // 发送移动数据到服务器（仅在Socket可用时）
        if ((moved || newFacing !== this.localPlayer.facing) && this.socket && this.socket.connected) {
            this.socket.emit('player-move', {
                x: this.localPlayer.sprite.x,
                y: this.localPlayer.sprite.y,
                facing: newFacing,
                isMoving: moved
            });
        }

        // 攻击控制 - 增强版
        if (Phaser.Input.Keyboard.JustDown(this.attackKeys.J)) {
            console.log('🥊 拳击攻击！');
            this.performAttack('punch');
        } else if (Phaser.Input.Keyboard.JustDown(this.attackKeys.K)) {
            console.log('🦵 踢腿攻击！');
            this.performAttack('kick');
        } else if (Phaser.Input.Keyboard.JustDown(this.attackKeys.L)) {
            console.log('⚡ 特殊攻击！');
            this.performAttack('special');
        }

        // 技能控制 - 增强版
        if (Phaser.Input.Keyboard.JustDown(this.skillKeys.ONE)) {
            console.log('🎯 使用技能！');
            this.performSkill();
        }

        // 新增格斗按键
        this.handleAdvancedCombat();
    }

    handleAdvancedCombat() {
        // 初始化连击系统
        if (!this.comboSystem) {
            this.comboSystem = {
                lastAttackTime: 0,
                comboCount: 0,
                comboWindow: 1000, // 1秒连击窗口
                maxCombo: 5
            };
        }

        // 检查数字键技能 (2-9)
        for (let i = 2; i <= 9; i++) {
            const key = this.input.keyboard.addKey(`DIGIT${i}`);
            if (Phaser.Input.Keyboard.JustDown(key)) {
                console.log(`🎯 使用技能 ${i}！`);
                this.performSpecialSkill(i);
            }
        }

        // 组合键攻击
        this.handleComboAttacks();

        // 防御系统
        this.handleDefense();

        // 闪避系统
        this.handleDodge();
    }

    handleComboAttacks() {
        const currentTime = Date.now();

        // 重置连击计数器如果超时
        if (currentTime - this.comboSystem.lastAttackTime > this.comboSystem.comboWindow) {
            this.comboSystem.comboCount = 0;
        }

        // 检查连击组合
        if (this.wasd.W.isDown && Phaser.Input.Keyboard.JustDown(this.attackKeys.J)) {
            console.log('🚀 上勾拳连击！');
            this.performComboAttack('uppercut');
        } else if (this.wasd.S.isDown && Phaser.Input.Keyboard.JustDown(this.attackKeys.K)) {
            console.log('🌪️ 下扫腿连击！');
            this.performComboAttack('sweep');
        } else if ((this.wasd.A.isDown || this.wasd.D.isDown) && Phaser.Input.Keyboard.JustDown(this.attackKeys.L)) {
            console.log('⚡ 侧身特殊攻击！');
            this.performComboAttack('side_special');
        }
    }

    handleDefense() {
        // 空格键防御
        const spaceKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.SPACE);
        if (spaceKey.isDown) {
            if (!this.localPlayer.isDefending) {
                console.log('🛡️ 开始防御！');
                this.localPlayer.isDefending = true;
                this.showDefenseEffect();
            }
        } else if (this.localPlayer.isDefending) {
            console.log('🛡️ 停止防御！');
            this.localPlayer.isDefending = false;
            this.hideDefenseEffect();
        }
    }

    handleDodge() {
        // Shift + 方向键闪避
        const shiftKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.SHIFT);

        if (shiftKey.isDown && !this.localPlayer.isDodging) {
            let dodgeDirection = null;

            if (this.wasd.W.isDown || this.cursors.up.isDown) {
                dodgeDirection = 'up';
            } else if (this.wasd.S.isDown || this.cursors.down.isDown) {
                dodgeDirection = 'down';
            } else if (this.wasd.A.isDown || this.cursors.left.isDown) {
                dodgeDirection = 'left';
            } else if (this.wasd.D.isDown || this.cursors.right.isDown) {
                dodgeDirection = 'right';
            }

            if (dodgeDirection) {
                console.log(`💨 向${dodgeDirection}闪避！`);
                this.performDodge(dodgeDirection);
            }
        }
    }

    performAttack(attackType) {
        if (this.localPlayer.isAttacking) return;

        this.localPlayer.isAttacking = true;

        // 更新连击系统
        this.updateComboSystem();

        // 播放攻击音效
        this.soundManager?.playSound(attackType);

        // 播放攻击动画
        this.playAttackAnimation(this.localPlayer, attackType);

        // 发送攻击数据到服务器（仅在Socket可用时）
        if (this.socket && this.socket.connected) {
            this.socket.emit('player-attack', {
                attackType: attackType,
                x: this.localPlayer.sprite.x,
                y: this.localPlayer.sprite.y,
                comboCount: this.comboSystem.comboCount
            });
        }

        // 重置攻击状态
        setTimeout(() => {
            if (this.localPlayer) {
                this.localPlayer.isAttacking = false;
            }
        }, 500);
    }

    performComboAttack(comboType) {
        if (this.localPlayer.isAttacking) return;

        console.log(`🔥 连击攻击: ${comboType}, 连击数: ${this.comboSystem.comboCount + 1}`);

        this.localPlayer.isAttacking = true;
        this.updateComboSystem();

        // 连击伤害加成
        const damageMultiplier = 1 + (this.comboSystem.comboCount * 0.2);

        // 播放连击特效
        this.playComboAnimation(this.localPlayer, comboType, this.comboSystem.comboCount);

        // 发送连击数据到服务器
        if (this.socket && this.socket.connected) {
            this.socket.emit('player-combo-attack', {
                comboType: comboType,
                comboCount: this.comboSystem.comboCount,
                damageMultiplier: damageMultiplier,
                x: this.localPlayer.sprite.x,
                y: this.localPlayer.sprite.y
            });
        }

        // 重置攻击状态
        setTimeout(() => {
            if (this.localPlayer) {
                this.localPlayer.isAttacking = false;
            }
        }, 600);
    }

    performSpecialSkill(skillNumber) {
        if (this.localPlayer.isUsingSkill) return;

        this.localPlayer.isUsingSkill = true;

        const specialSkills = {
            2: { name: '火球术', effect: 'fireball', emoji: '🔥' },
            3: { name: '冰锥术', effect: 'icespike', emoji: '❄️' },
            4: { name: '雷电术', effect: 'lightning', emoji: '⚡' },
            5: { name: '治疗术', effect: 'heal', emoji: '💚' },
            6: { name: '护盾术', effect: 'shield', emoji: '🛡️' },
            7: { name: '瞬移术', effect: 'teleport', emoji: '💫' },
            8: { name: '狂暴术', effect: 'rage', emoji: '😡' },
            9: { name: '终极技', effect: 'ultimate', emoji: '💥' }
        };

        const skill = specialSkills[skillNumber];
        if (skill) {
            console.log(`${skill.emoji} 使用${skill.name}！`);
            this.createSpecialSkillEffect(skill.effect, this.localPlayer.sprite.x, this.localPlayer.sprite.y, this.localPlayer.facing);

            // 发送特殊技能数据到服务器
            if (this.socket && this.socket.connected) {
                this.socket.emit('player-special-skill', {
                    skillNumber: skillNumber,
                    skillType: skill.effect,
                    x: this.localPlayer.sprite.x,
                    y: this.localPlayer.sprite.y,
                    facing: this.localPlayer.facing
                });
            }
        }

        // 重置技能状态
        setTimeout(() => {
            if (this.localPlayer) {
                this.localPlayer.isUsingSkill = false;
            }
        }, 1500);
    }

    performDodge(direction) {
        if (this.localPlayer.isDodging) return;

        this.localPlayer.isDodging = true;

        // 闪避距离
        const dodgeDistance = 100;
        let newX = this.localPlayer.sprite.x;
        let newY = this.localPlayer.sprite.y;

        switch (direction) {
            case 'up':
                newY = Math.max(50, newY - dodgeDistance);
                break;
            case 'down':
                newY = Math.min(550, newY + dodgeDistance);
                break;
            case 'left':
                newX = Math.max(50, newX - dodgeDistance);
                break;
            case 'right':
                newX = Math.min(750, newX + dodgeDistance);
                break;
        }

        // 闪避动画
        this.tweens.add({
            targets: this.localPlayer.sprite,
            x: newX,
            y: newY,
            duration: 200,
            ease: 'Power2',
            onStart: () => {
                // 闪避时半透明
                this.localPlayer.sprite.setAlpha(0.5);
            },
            onComplete: () => {
                // 恢复不透明
                this.localPlayer.sprite.setAlpha(1);
                this.localPlayer.isDodging = false;
            }
        });

        // 发送闪避数据到服务器
        if (this.socket && this.socket.connected) {
            this.socket.emit('player-dodge', {
                direction: direction,
                x: newX,
                y: newY
            });
        }
    }

    updateComboSystem() {
        const currentTime = Date.now();

        // 如果在连击窗口内，增加连击数
        if (currentTime - this.comboSystem.lastAttackTime <= this.comboSystem.comboWindow) {
            this.comboSystem.comboCount = Math.min(this.comboSystem.comboCount + 1, this.comboSystem.maxCombo);
        } else {
            this.comboSystem.comboCount = 1;
        }

        this.comboSystem.lastAttackTime = currentTime;

        // 显示连击数
        if (this.comboSystem.comboCount > 1) {
            this.showComboCounter();
        }
    }

    showComboCounter() {
        // 移除之前的连击显示
        if (this.comboText) {
            this.comboText.destroy();
        }

        // 创建新的连击显示
        this.comboText = this.add.text(this.localPlayer.sprite.x, this.localPlayer.sprite.y - 120,
            `${this.comboSystem.comboCount} COMBO!`, {
            fontSize: '20px',
            fill: '#FFD700',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        // 动画效果
        this.tweens.add({
            targets: this.comboText,
            scale: 1.5,
            alpha: 0,
            y: this.comboText.y - 30,
            duration: 1000,
            onComplete: () => {
                if (this.comboText) {
                    this.comboText.destroy();
                    this.comboText = null;
                }
            }
        });
    }

    playComboAnimation(player, comboType, comboCount) {
        if (!player.sprite) return;

        const x = player.facing === 'right' ? player.sprite.x + 60 : player.sprite.x - 60;
        const y = player.sprite.y - 20;

        // 连击特效
        const comboEffects = {
            'uppercut': '🚀',
            'sweep': '🌪️',
            'side_special': '⚡'
        };

        const effectText = this.add.text(x, y, comboEffects[comboType] || '💥', {
            fontSize: `${24 + comboCount * 4}px`
        }).setOrigin(0.5);

        // 连击动画更华丽
        this.tweens.add({
            targets: effectText,
            alpha: 0,
            scale: 2 + comboCount * 0.3,
            rotation: Math.PI * 2,
            duration: 600,
            onComplete: () => {
                effectText.destroy();
            }
        });

        // 屏幕震动效果
        this.cameras.main.shake(100 + comboCount * 50, 0.01);
    }

    showDefenseEffect() {
        if (this.defenseShield) return;

        // 创建防御护盾效果
        this.defenseShield = this.add.circle(
            this.localPlayer.sprite.x,
            this.localPlayer.sprite.y,
            60,
            0x00BFFF,
            0.3
        );
        this.defenseShield.setStrokeStyle(3, 0x00BFFF, 0.8);
        this.defenseShield.setDepth(15);

        // 护盾脉动动画
        this.tweens.add({
            targets: this.defenseShield,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 500,
            yoyo: true,
            repeat: -1
        });
    }

    hideDefenseEffect() {
        if (this.defenseShield) {
            this.defenseShield.destroy();
            this.defenseShield = null;
        }
    }

    createSpecialSkillEffect(skillType, x, y, facing) {
        switch (skillType) {
            case 'fireball':
                this.createFireballEffect(x, y, facing);
                break;
            case 'icespike':
                this.createIceSpikeEffect(x, y, facing);
                break;
            case 'lightning':
                this.createLightningEffect(x, y);
                break;
            case 'heal':
                this.createHealEffect(x, y);
                break;
            case 'shield':
                this.createShieldEffect(x, y);
                break;
            case 'teleport':
                this.createTeleportEffect(x, y);
                break;
            case 'rage':
                this.createRageEffect(x, y);
                break;
            case 'ultimate':
                this.createUltimateEffect(x, y);
                break;
        }
    }

    createFireballEffect(x, y, facing) {
        const fireball = this.add.circle(x, y, 15, 0xFF4500);
        fireball.setStrokeStyle(3, 0xFF6600);

        const targetX = facing === 'right' ? x + 300 : x - 300;

        this.tweens.add({
            targets: fireball,
            x: targetX,
            duration: 1000,
            onComplete: () => {
                // 爆炸效果
                for (let i = 0; i < 8; i++) {
                    const spark = this.add.circle(fireball.x, fireball.y, 5, 0xFF4500);
                    this.tweens.add({
                        targets: spark,
                        x: spark.x + (Math.random() - 0.5) * 100,
                        y: spark.y + (Math.random() - 0.5) * 100,
                        alpha: 0,
                        duration: 500,
                        onComplete: () => spark.destroy()
                    });
                }
                fireball.destroy();
            }
        });
    }

    createIceSpikeEffect(x, y, facing) {
        const spikes = [];
        const direction = facing === 'right' ? 1 : -1;

        for (let i = 0; i < 5; i++) {
            const spike = this.add.triangle(
                x + (i * 40 * direction),
                y + 20,
                0, 0,
                10, 30,
                20, 0,
                0x87CEEB
            );
            spike.setStrokeStyle(2, 0x4682B4);
            spikes.push(spike);

            // 冰刺生长动画
            spike.setScale(0);
            this.tweens.add({
                targets: spike,
                scaleY: 1,
                duration: 200,
                delay: i * 100,
                onComplete: () => {
                    setTimeout(() => {
                        this.tweens.add({
                            targets: spike,
                            alpha: 0,
                            duration: 300,
                            onComplete: () => spike.destroy()
                        });
                    }, 1000);
                }
            });
        }
    }

    createLightningEffect(x, y) {
        // 创建闪电效果
        const lightning = this.add.graphics();
        lightning.lineStyle(4, 0xFFFF00, 1);

        // 绘制锯齿状闪电
        lightning.beginPath();
        lightning.moveTo(x, y - 200);
        for (let i = 0; i < 10; i++) {
            const nextX = x + (Math.random() - 0.5) * 40;
            const nextY = y - 200 + (i * 20);
            lightning.lineTo(nextX, nextY);
        }
        lightning.strokePath();

        // 闪电闪烁效果
        this.tweens.add({
            targets: lightning,
            alpha: 0,
            duration: 100,
            yoyo: true,
            repeat: 5,
            onComplete: () => lightning.destroy()
        });

        // 屏幕闪白效果
        const flash = this.add.rectangle(400, 300, 800, 600, 0xFFFFFF, 0.5);
        this.tweens.add({
            targets: flash,
            alpha: 0,
            duration: 200,
            onComplete: () => flash.destroy()
        });
    }

    createHealEffect(x, y) {
        // 治疗光环效果
        for (let i = 0; i < 6; i++) {
            const healParticle = this.add.circle(x, y, 8, 0x00FF00, 0.8);

            this.tweens.add({
                targets: healParticle,
                x: x + Math.cos(i * Math.PI / 3) * 50,
                y: y + Math.sin(i * Math.PI / 3) * 50,
                alpha: 0,
                duration: 1000,
                onComplete: () => healParticle.destroy()
            });
        }

        // 治疗数字显示
        const healText = this.add.text(x, y - 50, '+25', {
            fontSize: '20px',
            fill: '#00FF00',
            fontStyle: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: healText,
            y: healText.y - 30,
            alpha: 0,
            duration: 1000,
            onComplete: () => healText.destroy()
        });
    }

    createShieldEffect(x, y) {
        // 护盾效果
        const shield = this.add.circle(x, y, 60, 0x4169E1, 0.3);
        shield.setStrokeStyle(4, 0x4169E1, 0.8);

        // 护盾旋转效果
        this.tweens.add({
            targets: shield,
            rotation: Math.PI * 2,
            duration: 2000,
            repeat: 2,
            onComplete: () => {
                this.tweens.add({
                    targets: shield,
                    alpha: 0,
                    duration: 500,
                    onComplete: () => shield.destroy()
                });
            }
        });
    }

    createTeleportEffect(x, y) {
        // 瞬移特效
        const teleportCircle = this.add.circle(x, y, 80, 0x9400D3, 0.5);

        this.tweens.add({
            targets: teleportCircle,
            scaleX: 0,
            scaleY: 0,
            duration: 300,
            onComplete: () => teleportCircle.destroy()
        });

        // 随机瞬移位置
        const newX = 100 + Math.random() * 600;
        const newY = 100 + Math.random() * 400;

        // 瞬移玩家
        if (this.localPlayer && this.localPlayer.sprite) {
            this.localPlayer.sprite.setPosition(newX, newY);

            // 出现特效
            const appearCircle = this.add.circle(newX, newY, 80, 0x9400D3, 0.5);
            this.tweens.add({
                targets: appearCircle,
                scaleX: 0,
                scaleY: 0,
                duration: 300,
                onComplete: () => appearCircle.destroy()
            });
        }
    }

    createRageEffect(x, y) {
        // 狂暴效果 - 红色光环
        const rageAura = this.add.circle(x, y, 70, 0xFF0000, 0.3);
        rageAura.setStrokeStyle(5, 0xFF0000, 0.8);

        // 狂暴脉动效果
        this.tweens.add({
            targets: rageAura,
            scaleX: 1.5,
            scaleY: 1.5,
            duration: 200,
            yoyo: true,
            repeat: 10,
            onComplete: () => rageAura.destroy()
        });

        // 增加攻击力提示
        const rageText = this.add.text(x, y - 60, '攻击力 +50%!', {
            fontSize: '16px',
            fill: '#FF0000',
            fontStyle: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: rageText,
            y: rageText.y - 30,
            alpha: 0,
            duration: 2000,
            onComplete: () => rageText.destroy()
        });
    }

    createUltimateEffect(x, y) {
        // 终极技能 - 华丽的爆炸效果
        this.cameras.main.shake(500, 0.02);

        // 多层爆炸圆圈
        for (let i = 0; i < 5; i++) {
            const explosion = this.add.circle(x, y, 20 + i * 30, 0xFFD700, 0.8 - i * 0.15);
            explosion.setStrokeStyle(3, 0xFF4500);

            this.tweens.add({
                targets: explosion,
                scaleX: 3 + i,
                scaleY: 3 + i,
                alpha: 0,
                duration: 800 + i * 200,
                onComplete: () => explosion.destroy()
            });
        }

        // 终极技能文字
        const ultimateText = this.add.text(x, y - 100, '💥 ULTIMATE! 💥', {
            fontSize: '32px',
            fill: '#FFD700',
            fontStyle: 'bold',
            stroke: '#FF4500',
            strokeThickness: 3
        }).setOrigin(0.5);

        this.tweens.add({
            targets: ultimateText,
            scaleX: 1.5,
            scaleY: 1.5,
            alpha: 0,
            duration: 2000,
            onComplete: () => ultimateText.destroy()
        });
    }

    performSkill() {
        if (this.localPlayer.isUsingSkill) return;

        this.localPlayer.isUsingSkill = true;
        const skillType = this.getRandomSkill();
        
        // 播放技能音效
        this.soundManager?.playSound('skill');
        
        // 发送技能数据到服务器（仅在Socket可用时）
        if (this.socket && this.socket.connected) {
            this.socket.emit('player-skill', {
                skillType: skillType,
                x: this.localPlayer.sprite.x,
                y: this.localPlayer.sprite.y,
                facing: this.localPlayer.facing
            });
        }

        // 创建技能特效
        this.createSkillEffect(skillType, this.localPlayer.sprite.x, this.localPlayer.sprite.y, this.localPlayer.facing);

        // 重置技能状态
        setTimeout(() => {
            if (this.localPlayer) {
                this.localPlayer.isUsingSkill = false;
            }
        }, 1000);
    }

    getRandomSkill() {
        const skills = ['missile', 'fire', 'ice'];
        return skills[Math.floor(Math.random() * skills.length)];
    }

    // Socket事件处理方法
    handleRoomUpdate(data) {
        console.log('🔄 房间更新 - 玩家数量:', data.players.length);

        // 避免频繁重建 - 只在玩家数量变化时才重建
        const currentPlayerIds = Object.keys(this.players);
        const newPlayerIds = data.players.map(p => p.id);

        const playersChanged = currentPlayerIds.length !== newPlayerIds.length ||
                              !currentPlayerIds.every(id => newPlayerIds.includes(id));

        if (!playersChanged) {
            console.log('玩家列表未变化，跳过重建');
            return;
        }

        console.log('玩家列表变化，重建玩家对象');

        // 清理旧的玩家对象
        Object.values(this.players).forEach(player => {
            if (player.sprite) player.sprite.destroy();
            if (player.healthBarBg) player.healthBarBg.destroy();
            if (player.healthBar) player.healthBar.destroy();
            if (player.nameText) player.nameText.destroy();
        });
        this.players = {};

        data.players.forEach((player, index) => {
            console.log(`处理玩家 ${index + 1}:`, player);
            this.createOrUpdatePlayer(player);
            if (player.id === this.socket.id) {
                this.localPlayer = this.players[player.id];
                console.log('设置本地玩家:', this.localPlayer);
            }
        });

        console.log('✅ 房间更新完成，当前玩家对象:', Object.keys(this.players));
    }

    handleGameStart(data) {
        console.log('游戏开始:', data);
        this.soundManager?.playSound('gameStart');
    }

    handlePlayerUpdate(data) {
        if (this.players[data.playerId] && this.players[data.playerId].sprite) {
            const player = this.players[data.playerId];
            player.sprite.setPosition(data.playerState.x, data.playerState.y);
            player.facing = data.playerState.facing;
            player.sprite.setFlipX(data.playerState.facing === 'left');
        }
    }

    handlePlayerAttack(data) {
        if (this.players[data.playerId]) {
            this.playAttackAnimation(this.players[data.playerId], data.attackType);
        }
    }

    handlePlayerSkill(data) {
        if (this.players[data.playerId]) {
            this.createSkillEffect(data.skillType, data.x, data.y, data.facing);
        }
    }

    handlePlayerDamage(data) {
        if (this.players[data.playerId]) {
            this.showDamageEffect(this.players[data.playerId], data.damage);
            this.soundManager?.playSound('hit');
        }
    }

    handleGameEnd(data) {
        console.log('游戏结束:', data);
        const isWinner = data.winnerId === this.socket.id;
        this.soundManager?.playSound(isWinner ? 'victory' : 'defeat');

        // 显示游戏结束UI
        this.showGameEndScreen(data.winner, isWinner);
    }

    createTempCharacterTexture(playerId) {
        // 为每个玩家创建一个独特颜色的临时角色
        const colors = [0xFF5722, 0x2196F3, 0x4CAF50, 0xFF9800, 0x9C27B0];
        const color = colors[playerId.charCodeAt(0) % colors.length];

        const graphics = this.add.graphics();
        graphics.fillStyle(color);
        graphics.fillRect(0, 0, 40, 60);

        // 添加一个简单的脸
        graphics.fillStyle(0xFFFFFF);
        graphics.fillCircle(15, 20, 3); // 左眼
        graphics.fillCircle(25, 20, 3); // 右眼
        graphics.fillRect(18, 25, 4, 2); // 嘴巴

        graphics.generateTexture(`temp-char-${playerId}`, 40, 60);
        graphics.destroy();

        console.log(`为玩家 ${playerId} 创建临时角色纹理`);
    }

    createOrUpdatePlayer(playerData) {
        console.log('创建/更新玩家:', playerData);

        if (!this.players[playerData.id]) {
            console.log(`创建新玩家: ${playerData.name}, 角色: ${playerData.character}, 位置: (${playerData.x}, ${playerData.y})`);

            // 检查角色图片是否存在
            let characterTexture = playerData.character;
            if (!this.textures.exists(characterTexture)) {
                console.error(`角色图片不存在: ${characterTexture}`);
                // 尝试使用part_01作为备用
                if (this.textures.exists('part_01')) {
                    characterTexture = 'part_01';
                    console.log('使用part_01作为备用角色');
                } else if (this.textures.exists('simple-character')) {
                    // 使用简单角色图片
                    characterTexture = 'simple-character';
                    console.log('使用简单角色图片');
                } else {
                    // 最后的备用方案：创建一个临时的彩色方块
                    console.warn('所有角色图片都不可用，创建临时角色');
                    this.createTempCharacterTexture(playerData.id);
                    characterTexture = `temp-char-${playerData.id}`;
                }
            }

            try {
                // 创建新玩家精灵
                const sprite = this.physics.add.sprite(playerData.x, playerData.y, characterTexture);
                sprite.setScale(0.2); // 调整角色大小为合适比例（缩小4倍）
                sprite.setCollideWorldBounds(true);
                sprite.setFlipX(playerData.facing === 'left');
                sprite.setDepth(10); // 确保角色在前景

                console.log(`玩家精灵创建成功: ${playerData.name}`);

                // 创建血条背景
                const healthBarBg = this.add.rectangle(playerData.x, playerData.y - 80, 80, 8, 0x000000, 0.5);
                healthBarBg.setDepth(20);

                // 创建血条
                const healthBar = this.add.rectangle(playerData.x, playerData.y - 80, 80, 8, 0x4CAF50);
                healthBar.setDepth(21);

                // 创建玩家名字
                const nameText = this.add.text(playerData.x, playerData.y - 100, playerData.name, {
                    fontSize: '14px',
                    fill: '#ffffff',
                    stroke: '#000000',
                    strokeThickness: 2
                }).setOrigin(0.5);
                nameText.setDepth(22);

                this.players[playerData.id] = {
                    ...playerData,
                    sprite: sprite,
                    healthBarBg: healthBarBg,
                    healthBar: healthBar,
                    nameText: nameText
                };

                console.log(`玩家 ${playerData.name} 创建完成，精灵可见性:`, sprite.visible);
                console.log(`精灵位置: (${sprite.x}, ${sprite.y}), 缩放: ${sprite.scale}, 深度: ${sprite.depth}`);

            } catch (error) {
                console.error('创建玩家精灵时出错:', error);
            }
        } else {
            // 更新现有玩家
            const player = this.players[playerData.id];
            Object.assign(player, playerData);

            if (player.sprite) {
                player.sprite.setPosition(playerData.x, playerData.y);
                player.sprite.setFlipX(playerData.facing === 'left');

                // 更新血条和名字位置
                if (player.healthBarBg) player.healthBarBg.setPosition(playerData.x, playerData.y - 80);
                if (player.healthBar) player.healthBar.setPosition(playerData.x, playerData.y - 80);
                if (player.nameText) player.nameText.setPosition(playerData.x, playerData.y - 100);
            }
        }
    }

    playAttackAnimation(player, attackType) {
        if (!player.sprite) return;

        // 创建攻击特效
        const x = player.facing === 'right' ? player.sprite.x + 60 : player.sprite.x - 60;
        const y = player.sprite.y - 20;

        // 根据攻击类型显示不同特效
        const effectEmoji = {
            'punch': '👊',
            'kick': '🦵',
            'special': '⚡'
        };

        const effectText = this.add.text(x, y, effectEmoji[attackType] || '💥', {
            fontSize: '24px'
        }).setOrigin(0.5);

        // 动画效果
        this.tweens.add({
            targets: effectText,
            alpha: 0,
            scale: 2,
            duration: 500,
            onComplete: () => {
                effectText.destroy();
            }
        });

        // 暂时禁用玩家闪烁效果以避免持续闪烁
        // this.tweens.add({
        //     targets: player.sprite,
        //     alpha: 0.5,
        //     duration: 100,
        //     yoyo: true,
        //     repeat: 2
        // });
    }

    createSkillEffect(skillType, x, y, facing) {
        switch (skillType) {
            case 'missile':
                this.createMissileEffect(x, y, facing);
                break;
            case 'fire':
                this.createFireEffect(x, y, facing);
                break;
            case 'ice':
                this.createIceEffect(x, y, facing);
                break;
        }
    }

    createMissileEffect(x, y, facing) {
        const missile = this.add.image(x, y - 50, 'skill-01');
        missile.setScale(0.5);
        missile.setFlipX(facing === 'left');

        const targetX = facing === 'right' ? x + 400 : x - 400;

        this.tweens.add({
            targets: missile,
            x: targetX,
            duration: 2000,
            onComplete: () => {
                // 爆炸效果
                const explosion = this.add.circle(missile.x, missile.y, 30, 0xff6600, 0.8);
                this.tweens.add({
                    targets: explosion,
                    scale: 2,
                    alpha: 0,
                    duration: 300,
                    onComplete: () => {
                        explosion.destroy();
                    }
                });
                missile.destroy();
            }
        });
    }

    createFireEffect(x, y, facing) {
        const fireX = facing === 'right' ? x + 80 : x - 80;
        const fire = this.add.image(fireX, y - 60, 'skill-02');
        fire.setScale(0.8);

        // 火焰跳动动画
        this.tweens.add({
            targets: fire,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 200,
            yoyo: true,
            repeat: 7,
            onComplete: () => {
                fire.destroy();
            }
        });

        // 添加粒子效果
        this.createFireParticles(fireX, y - 60);
    }

    createIceEffect(x, y, facing) {
        const iceX = facing === 'right' ? x + 100 : x - 100;
        const ice = this.add.image(iceX, y - 40, 'skill-03');
        ice.setScale(0.6);

        // 冰块生长动画
        this.tweens.add({
            targets: ice,
            scale: 1.2,
            duration: 2500,
            onComplete: () => {
                // 冰块破碎效果
                this.tweens.add({
                    targets: ice,
                    alpha: 0,
                    angle: 45,
                    duration: 300,
                    onComplete: () => {
                        ice.destroy();
                    }
                });
            }
        });

        // 冰晶环绕效果
        const circle = this.add.circle(iceX, y - 40, 30);
        circle.setStrokeStyle(2, 0xADD8E6, 0.8);
        this.tweens.add({
            targets: circle,
            scaleX: 1.5,
            scaleY: 1.5,
            alpha: 0,
            duration: 2500,
            onComplete: () => {
                circle.destroy();
            }
        });
    }

    createFireParticles(x, y) {
        // 简单的火花效果
        for (let i = 0; i < 5; i++) {
            const spark = this.add.circle(
                x + (Math.random() - 0.5) * 60,
                y + (Math.random() - 0.5) * 60,
                2,
                0xff6600
            );

            this.tweens.add({
                targets: spark,
                alpha: 0,
                y: spark.y - 20,
                duration: 1000,
                delay: i * 100,
                onComplete: () => {
                    spark.destroy();
                }
            });
        }
    }

    showDamageEffect(player, damage) {
        if (!player.sprite) return;

        // 显示伤害数字
        const damageText = this.add.text(player.sprite.x, player.sprite.y - 50, `-${damage}`, {
            fontSize: '20px',
            fill: '#ff0000',
            fontStyle: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: damageText,
            y: damageText.y - 30,
            alpha: 0,
            duration: 1000,
            onComplete: () => {
                damageText.destroy();
            }
        });

        // 更新血条
        const healthPercent = player.health / 100;
        this.tweens.add({
            targets: player.healthBar,
            scaleX: healthPercent,
            duration: 300
        });

        // 改变血条颜色
        const color = healthPercent > 0.5 ? 0x4CAF50 :
                     healthPercent > 0.25 ? 0xFF9800 : 0xF44336;
        player.healthBar.setFillStyle(color);
    }

    showGameEndScreen(winner, isWinner) {
        // 创建游戏结束覆盖层
        const overlay = this.add.rectangle(400, 300, 800, 600, 0x000000, 0.7);

        const resultText = this.add.text(400, 250, isWinner ? '🎉 你赢了！' : '😢 你输了！', {
            fontSize: '48px',
            fill: isWinner ? '#4CAF50' : '#F44336',
            fontStyle: 'bold'
        }).setOrigin(0.5);

        const winnerText = this.add.text(400, 320, `获胜者: ${winner}`, {
            fontSize: '24px',
            fill: '#ffffff'
        }).setOrigin(0.5);

        const restartText = this.add.text(400, 380, '点击任意键重新开始', {
            fontSize: '18px',
            fill: '#ffffff',
            alpha: 0.8
        }).setOrigin(0.5);

        // 闪烁效果
        this.tweens.add({
            targets: restartText,
            alpha: 0.3,
            duration: 1000,
            yoyo: true,
            repeat: -1
        });

        // 监听重新开始
        this.input.keyboard.once('keydown', () => {
            // 重新加载页面或重置游戏
            window.location.reload();
        });
    }

    updateSkillEffects() {
        // 更新技能效果（如果需要持续更新的话）
    }

    // 公共方法，供外部调用
    joinGame(playerData) {
        this.socket.emit('join-game', playerData);
    }
}

// 将GameScene暴露到全局作用域
window.GameScene = GameScene;

// 音效管理器类
class PhaserSoundManager {
    constructor() {
        this.audioContext = null;
        this.isMuted = false;
        this.effectVolume = 0.5;
        this.initAudio();
    }

    initAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.warn('Web Audio API not supported');
        }
    }

    playSound(type) {
        if (!this.audioContext || this.isMuted) return;

        switch (type) {
            case 'punch':
                this.createPunchSound();
                break;
            case 'kick':
                this.createKickSound();
                break;
            case 'special':
                this.createSpecialSound();
                break;
            case 'skill':
                this.createSkillSound();
                break;
            case 'hit':
                this.createHitSound();
                break;
        }
    }

    createPunchSound() {
        // 简化的音效创建
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(100, this.audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(this.effectVolume, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.1);
    }

    // 其他音效方法...
    createKickSound() { /* 实现踢腿音效 */ }
    createSpecialSound() { /* 实现特殊技能音效 */ }
    createSkillSound() { /* 实现技能音效 */ }
    createHitSound() { /* 实现受伤音效 */ }
}

// 将PhaserSoundManager也暴露到全局作用域
window.PhaserSoundManager = PhaserSoundManager;
