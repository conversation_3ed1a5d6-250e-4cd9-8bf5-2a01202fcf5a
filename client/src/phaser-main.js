// Phaser.js主配置和启动文件

class BrainrotFightingGamePhaser {
    constructor() {
        this.game = null;
        this.gameScene = null;
        this.playerName = '';
        this.selectedCharacter = '';
        this.menuSoundManager = null;
        this.init();
    }

    init() {
        this.initMenuSounds();
        this.setupEventListeners();
        this.initPhaserGame();

        // 自动启动游戏进行测试
        this.autoStartGame();
    }

    initMenuSounds() {
        // 初始化菜单音效管理器
        try {
            this.menuSoundManager = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.warn('Web Audio API not supported');
        }
    }

    playMenuSound(type) {
        if (!this.menuSoundManager) return;

        const oscillator = this.menuSoundManager.createOscillator();
        const gainNode = this.menuSoundManager.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.menuSoundManager.destination);

        switch (type) {
            case 'select':
                // 角色选择音效
                oscillator.frequency.setValueAtTime(440, this.menuSoundManager.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(660, this.menuSoundManager.currentTime + 0.1);
                gainNode.gain.setValueAtTime(0.3, this.menuSoundManager.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, this.menuSoundManager.currentTime + 0.1);
                oscillator.start(this.menuSoundManager.currentTime);
                oscillator.stop(this.menuSoundManager.currentTime + 0.1);
                break;
            case 'confirm':
                // 确认音效
                oscillator.frequency.setValueAtTime(523, this.menuSoundManager.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(784, this.menuSoundManager.currentTime + 0.2);
                gainNode.gain.setValueAtTime(0.4, this.menuSoundManager.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, this.menuSoundManager.currentTime + 0.2);
                oscillator.start(this.menuSoundManager.currentTime);
                oscillator.stop(this.menuSoundManager.currentTime + 0.2);
                break;
            case 'error':
                // 错误音效
                oscillator.frequency.setValueAtTime(200, this.menuSoundManager.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(150, this.menuSoundManager.currentTime + 0.3);
                gainNode.gain.setValueAtTime(0.3, this.menuSoundManager.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, this.menuSoundManager.currentTime + 0.3);
                oscillator.start(this.menuSoundManager.currentTime);
                oscillator.stop(this.menuSoundManager.currentTime + 0.3);
                break;
        }
    }

    setupEventListeners() {
        // 角色选择事件
        document.querySelectorAll('.character-card').forEach(card => {
            card.addEventListener('click', () => {
                // 播放选择音效
                this.playMenuSound('select');

                // 移除之前的选择
                document.querySelectorAll('.character-card').forEach(c => c.classList.remove('selected'));

                // 添加当前选择
                card.classList.add('selected');
                this.selectedCharacter = card.dataset.character;

                // 显示选择确认信息
                this.showCharacterSelection(card);
                this.updateSelectionDisplay(card);
                this.enableJoinButton();

                console.log('选择角色:', this.selectedCharacter);
            });
        });

        // 开始游戏按钮
        document.getElementById('start-game-btn')?.addEventListener('click', () => {
            this.playMenuSound('select');
            this.resetSelection();
            this.showScreen('character-select');
        });

        // 加入游戏按钮
        document.getElementById('join-game-btn')?.addEventListener('click', () => {
            const nameInput = document.getElementById('player-name');
            this.playerName = nameInput.value.trim() || '匿名玩家';

            if (!this.selectedCharacter) {
                this.playMenuSound('error');
                this.showSelectionError();
                return;
            }

            this.playMenuSound('confirm');
            this.startPhaserGame();
        });

        // 返回菜单按钮
        document.getElementById('back-to-menu-btn')?.addEventListener('click', () => {
            this.playMenuSound('select');
            this.resetSelection();
            this.showScreen('main-menu');
        });

        // 静音按钮
        document.getElementById('mute-btn')?.addEventListener('click', (e) => {
            const btn = e.target;
            const isMuted = btn.textContent === '🔇';
            btn.textContent = isMuted ? '🔊' : '🔇';
            
            if (this.gameScene && this.gameScene.soundManager) {
                this.gameScene.soundManager.isMuted = !isMuted;
            }
        });
    }

    initPhaserGame() {
        // Phaser游戏配置
        const config = {
            type: Phaser.AUTO,
            width: 800,
            height: 600,
            parent: 'game-canvas-container', // 将游戏渲染到指定容器
            backgroundColor: '#87CEEB',
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 }, // 2D格斗游戏不需要重力
                    debug: false
                }
            },
            scene: GameScene
        };

        // 创建Phaser游戏实例
        this.game = new Phaser.Game(config);

        // 等待场景创建完成后获取引用并启动
        setTimeout(() => {
            console.log('🎮 开始启动游戏场景...');

            // 强制启动场景
            this.game.scene.start('GameScene');

            // 等待场景完全启动
            setTimeout(() => {
                this.gameScene = this.game.scene.getScene('GameScene');
                console.log('🔍 场景启动后状态:', {
                    scene: this.gameScene,
                    isActive: this.gameScene?.scene.isActive(),
                    isVisible: this.gameScene?.scene.isVisible(),
                    key: this.gameScene?.scene.key
                });

                if (this.gameScene) {
                    // 确保场景被激活
                    if (!this.gameScene.scene.isActive()) {
                        console.log('🔄 激活游戏场景...');
                        this.gameScene.scene.setActive(true);
                    }
                    console.log('✅ 游戏场景已成功启动');
                } else {
                    console.error('❌ 无法获取游戏场景');
                    // 尝试通过场景管理器获取
                    const scenes = this.game.scene.scenes;
                    console.log('所有场景:', scenes);
                    if (scenes.length > 0) {
                        this.gameScene = scenes[0];
                        if (!this.gameScene.scene.isActive()) {
                            this.gameScene.scene.setActive(true);
                        }
                        console.log('使用第一个场景:', this.gameScene);
                    }
                }
            }, 200);
        }, 100);

        console.log('Phaser游戏实例创建完成');
    }

    autoStartGame() {
        // 自动启动游戏进行测试
        console.log('🚀 自动启动游戏进行测试...');

        // 设置默认玩家信息
        this.playerName = '测试玩家';
        this.selectedCharacter = 'part_01';

        console.log('🎯 设置玩家信息:', { name: this.playerName, character: this.selectedCharacter });

        // 延迟启动，确保 Phaser 初始化完成
        setTimeout(() => {
            this.startPhaserGame();
        }, 1000);
    }

    startPhaserGame() {
        // 隐藏菜单，显示游戏画布
        this.showScreen('game-screen');

        // 确保游戏画布容器存在
        let container = document.getElementById('game-canvas-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'game-canvas-container';
            container.style.width = '800px';
            container.style.height = '600px';
            container.style.margin = '0 auto';
            container.style.border = '2px solid #333';
            container.style.borderRadius = '10px';
            container.style.overflow = 'hidden';

            const gameScreen = document.getElementById('game-screen');
            if (gameScreen) {
                gameScreen.appendChild(container);
            } else {
                // 如果没有游戏屏幕，直接添加到 body
                document.body.appendChild(container);
            }
        }

        // 重新创建Phaser游戏（如果需要）
        if (!this.game || this.game.isDestroyed) {
            this.initPhaserGame();
        }

        // 等待场景准备就绪后加入游戏
        setTimeout(() => {
            console.log('尝试加入游戏，场景状态:', this.gameScene);

            // 如果场景还没准备好，再次尝试获取
            if (!this.gameScene) {
                this.gameScene = this.game.scene.getScene('GameScene');
                if (!this.gameScene && this.game.scene.scenes.length > 0) {
                    this.gameScene = this.game.scene.scenes[0];
                }
                console.log('重新获取场景:', this.gameScene);
            }

            if (this.gameScene) {
                console.log('🎮 场景已准备，开始设置玩家');
                console.log('📝 玩家信息:', {
                    name: this.playerName,
                    character: this.selectedCharacter
                });

                // 检查场景的键盘系统
                console.log('🔧 检查场景键盘系统:', {
                    hasInput: !!this.gameScene.input,
                    hasKeyboard: !!this.gameScene.input?.keyboard,
                    setupControls: !!this.gameScene.setupControls,
                    addBasicKeyboardTest: !!this.gameScene.addBasicKeyboardTest
                });

                // 确保角色信息正确设置
                if (!this.selectedCharacter) {
                    console.log('⚠️ 角色信息为空，重新设置为 part_01');
                    this.selectedCharacter = 'part_01';
                }

                console.log('🎯 开始创建玩家...');

                // 立即创建本地玩家（不依赖Socket）
                this.createLocalPlayer();

                // 尝试Socket连接
                if (this.gameScene.socket) {
                    console.log('🌐 Socket已连接，发送加入游戏请求');
                    this.gameScene.joinGame({
                        name: this.playerName,
                        character: this.selectedCharacter
                    });
                } else {
                    console.log('⚠️ Socket未准备就绪，使用单机模式');
                    // 在单机模式下创建一个AI对手
                    this.createAIOpponent();
                }

                console.log('🎯 玩家创建流程完成');

                // 检查本地玩家是否正确创建
                setTimeout(() => {
                    console.log('🔍 检查本地玩家状态:', {
                        hasLocalPlayer: !!this.gameScene.localPlayer,
                        localPlayerId: this.gameScene.localPlayer?.id,
                        playersCount: Object.keys(this.gameScene.players || {}).length,
                        playerIds: Object.keys(this.gameScene.players || {})
                    });
                }, 500);
            } else {
                console.error('❌ 游戏场景未准备就绪');
            }
        }, 1500);
    }

    createLocalPlayer() {
        console.log('🎭 开始创建本地玩家...');
        console.log('🎭 玩家信息:', { name: this.playerName, character: this.selectedCharacter });
        console.log('🎭 游戏场景状态:', {
            gameScene: !!this.gameScene,
            createOrUpdatePlayer: !!this.gameScene?.createOrUpdatePlayer
        });

        const playerData = {
            id: 'local-player',
            name: this.playerName,
            character: this.selectedCharacter,
            x: 200,
            y: 400,
            facing: 'right',
            health: 100
        };

        console.log('🎭 准备创建玩家数据:', playerData);

        try {
            this.gameScene.createOrUpdatePlayer(playerData);
            console.log('🎭 createOrUpdatePlayer 调用成功');

            this.gameScene.localPlayer = this.gameScene.players['local-player'];
            console.log('🎭 设置本地玩家引用:', this.gameScene.localPlayer);

            console.log('🎭 当前所有玩家:', Object.keys(this.gameScene.players));

            // 启动位置监控
            if (this.gameScene.startPositionMonitor) {
                this.gameScene.startPositionMonitor();
                console.log('🎭 位置监控已启动');
            } else {
                console.log('⚠️ 位置监控方法不存在');
            }

            console.log('✅ 本地玩家创建完成:', this.gameScene.localPlayer);
        } catch (error) {
            console.error('❌ 创建本地玩家时出错:', error);
        }
    }

    createAIOpponent() {
        console.log('🤖 创建AI对手');

        // 随机选择一个不同的角色作为AI
        const characters = ['part_01', 'part_02', 'part_03', 'part_04', 'part_05', 'part_06',
                          'part_07', 'part_08', 'part_09', 'part_10', 'part_11'];
        const availableChars = characters.filter(char => char !== this.selectedCharacter);
        const aiCharacter = availableChars[Math.floor(Math.random() * availableChars.length)];

        const aiData = {
            id: 'ai-opponent',
            name: 'AI对手',
            character: aiCharacter,
            x: 600,
            y: 400,
            facing: 'left',
            health: 100
        };

        this.gameScene.createOrUpdatePlayer(aiData);

        console.log('✅ AI对手创建完成:', aiCharacter);
    }

    showCharacterSelection(card) {
        // 显示角色选择确认
        const characterName = card.querySelector('h3').textContent;
        const characterDesc = card.querySelector('p').textContent;

        // 更新或创建选择确认显示
        let selectionDisplay = document.getElementById('character-selection-display');
        if (!selectionDisplay) {
            selectionDisplay = document.createElement('div');
            selectionDisplay.id = 'character-selection-display';
            selectionDisplay.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(76, 175, 80, 0.9);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                z-index: 1000;
                font-weight: bold;
                max-width: 250px;
                animation: slideInRight 0.3s ease-out;
            `;
            document.body.appendChild(selectionDisplay);

            // 添加CSS动画
            if (!document.getElementById('selection-animation-style')) {
                const style = document.createElement('style');
                style.id = 'selection-animation-style';
                style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes pulse {
                        0%, 100% { transform: scale(1); }
                        50% { transform: scale(1.05); }
                    }
                `;
                document.head.appendChild(style);
            }
        }

        selectionDisplay.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 20px;">✓</span>
                <div>
                    <div style="font-size: 14px; margin-bottom: 5px;">${characterName}</div>
                    <div style="font-size: 12px; opacity: 0.9;">${characterDesc}</div>
                </div>
            </div>
        `;

        // 添加脉冲动画
        selectionDisplay.style.animation = 'pulse 0.5s ease-in-out';

        // 移除动画
        setTimeout(() => {
            if (selectionDisplay) {
                selectionDisplay.style.animation = '';
            }
        }, 500);
    }

    showSelectionError() {
        // 显示选择错误提示
        let errorDisplay = document.getElementById('selection-error-display');
        if (!errorDisplay) {
            errorDisplay = document.createElement('div');
            errorDisplay.id = 'selection-error-display';
            errorDisplay.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(244, 67, 54, 0.95);
                color: white;
                padding: 20px 30px;
                border-radius: 15px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.4);
                z-index: 1001;
                font-weight: bold;
                text-align: center;
                animation: shake 0.5s ease-in-out;
            `;
            document.body.appendChild(errorDisplay);

            // 添加摇摆动画
            if (!document.getElementById('error-animation-style')) {
                const style = document.createElement('style');
                style.id = 'error-animation-style';
                style.textContent = `
                    @keyframes shake {
                        0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
                        25% { transform: translate(-50%, -50%) rotate(-2deg); }
                        75% { transform: translate(-50%, -50%) rotate(2deg); }
                    }
                `;
                document.head.appendChild(style);
            }
        }

        errorDisplay.innerHTML = `
            <div style="font-size: 18px; margin-bottom: 10px;">⚠️ 请选择角色</div>
            <div style="font-size: 14px; opacity: 0.9;">点击任意角色卡片进行选择</div>
        `;

        errorDisplay.style.display = 'block';

        // 3秒后自动隐藏
        setTimeout(() => {
            if (errorDisplay) {
                errorDisplay.style.display = 'none';
            }
        }, 3000);

        // 点击隐藏
        errorDisplay.onclick = () => {
            errorDisplay.style.display = 'none';
        };
    }

    updateSelectionDisplay(card) {
        const selectionContainer = document.getElementById('current-selection');
        if (!selectionContainer) return;

        const characterName = card.querySelector('h3').textContent;
        const characterDesc = card.querySelector('p').textContent;
        const characterImg = card.querySelector('img').src;

        // 更新选择显示
        selectionContainer.classList.add('has-selection');
        selectionContainer.innerHTML = `
            <div class="selected-character">
                <img src="${characterImg}" alt="${characterName}">
                <div class="selected-character-info">
                    <h4>${characterName}</h4>
                    <p>${characterDesc}</p>
                </div>
                <span style="color: #4CAF50; font-size: 20px;">✓</span>
            </div>
        `;
    }

    enableJoinButton() {
        const joinBtn = document.getElementById('join-game-btn');
        if (joinBtn) {
            joinBtn.disabled = false;
            joinBtn.style.background = 'linear-gradient(135deg, #4CAF50, #66BB6A)';
            joinBtn.style.color = '#ffffff';
            joinBtn.style.cursor = 'pointer';
        }
    }

    resetSelection() {
        // 重置角色选择
        this.selectedCharacter = '';

        // 移除所有选中状态
        document.querySelectorAll('.character-card').forEach(c => c.classList.remove('selected'));

        // 重置选择显示
        const selectionContainer = document.getElementById('current-selection');
        if (selectionContainer) {
            selectionContainer.classList.remove('has-selection');
            selectionContainer.innerHTML = '<span class="selection-hint">👆 请选择一个角色</span>';
        }

        // 禁用加入游戏按钮
        const joinBtn = document.getElementById('join-game-btn');
        if (joinBtn) {
            joinBtn.disabled = true;
            joinBtn.style.background = '';
            joinBtn.style.color = '';
            joinBtn.style.cursor = '';
        }

        // 隐藏选择确认显示
        const selectionDisplay = document.getElementById('character-selection-display');
        if (selectionDisplay) {
            selectionDisplay.remove();
        }
    }

    showScreen(screenId) {
        // 隐藏所有屏幕
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.add('hidden');
        });

        // 显示指定屏幕
        const targetScreen = document.getElementById(screenId);
        if (targetScreen) {
            targetScreen.classList.remove('hidden');
        } else {
            console.log(`屏幕 ${screenId} 不存在，跳过显示`);
        }
    }

    // 销毁游戏实例
    destroyGame() {
        if (this.game && !this.game.isDestroyed) {
            this.game.destroy(true);
            this.game = null;
            this.gameScene = null;
        }
    }
}

// 页面加载完成后启动游戏
document.addEventListener('DOMContentLoaded', () => {
    // 立即添加基础键盘测试
    console.log('🔧 添加基础键盘测试（页面级别）...');

    document.addEventListener('keydown', (event) => {
        console.log('🌍 页面级键盘事件 - KeyDown:', {
            key: event.key,
            code: event.code,
            timestamp: Date.now(),
            target: event.target.tagName
        });

        // 直接在页面级处理移动
        if (window.brainrotGame && window.brainrotGame.gameScene) {
            window.brainrotGame.gameScene.handlePageLevelMovement(event.key);
        }
    });

    document.addEventListener('keyup', (event) => {
        console.log('🌍 页面级键盘事件 - KeyUp:', {
            key: event.key,
            code: event.code,
            timestamp: Date.now(),
            target: event.target.tagName
        });

        // 处理按键释放事件（主要是防御）
        if (window.brainrotGame && window.brainrotGame.gameScene) {
            window.brainrotGame.gameScene.handlePageLevelKeyUp(event.key);
        }
    });

    console.log('✅ 页面级键盘测试设置完成，请按任意键测试');

    // 检查是否支持Phaser
    if (typeof Phaser === 'undefined') {
        console.error('Phaser.js未加载！请检查CDN链接。');
        alert('游戏引擎加载失败，请刷新页面重试。');
        return;
    }

    console.log('🎮 启动Phaser.js版本的意大利脑残山海经格斗游戏');
    console.log('Phaser版本:', Phaser.VERSION);

    // 创建游戏实例
    window.brainrotGame = new BrainrotFightingGamePhaser();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.brainrotGame) {
        window.brainrotGame.destroyGame();
    }
});
