<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>键盘输入测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-area {
            border: 2px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            min-height: 100px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .key-display {
            display: inline-block;
            background: #333;
            color: white;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 键盘输入调试测试</h1>
        
        <div class="status" id="status">
            点击下面的测试区域，然后按键盘测试...
        </div>

        <div class="test-area" id="testArea" tabindex="0">
            <h3>测试区域 - 点击这里获得焦点，然后按键</h3>
            <p>按下的键将显示在下方日志中</p>
            <div id="pressedKeys"></div>
        </div>

        <div class="log" id="log"></div>

        <div>
            <button onclick="clearLog()">清空日志</button>
            <button onclick="testGameKeyboard()">测试游戏键盘</button>
            <button onclick="simulateGameInput()">模拟游戏输入</button>
        </div>

        <div id="gameTest" style="margin-top: 20px;"></div>
    </div>

    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        const testArea = document.getElementById('testArea');
        const pressedKeys = document.getElementById('pressedKeys');

        let keyLog = [];

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            keyLog.push(logEntry);
            
            const logElement = document.createElement('div');
            logElement.textContent = logEntry;
            if (type === 'error') logElement.style.color = '#ff6b6b';
            if (type === 'success') logElement.style.color = '#51cf66';
            if (type === 'warning') logElement.style.color = '#ffd43b';
            
            log.appendChild(logElement);
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            log.innerHTML = '';
            keyLog = [];
            pressedKeys.innerHTML = '';
        }

        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // 基础键盘事件监听
        document.addEventListener('keydown', (e) => {
            addLog(`🔽 KeyDown: ${e.key} (code: ${e.code})`, 'info');
            
            // 显示按下的键
            const keySpan = document.createElement('span');
            keySpan.className = 'key-display';
            keySpan.textContent = e.key;
            pressedKeys.appendChild(keySpan);
        });

        document.addEventListener('keyup', (e) => {
            addLog(`🔼 KeyUp: ${e.key} (code: ${e.code})`, 'success');
        });

        // 测试区域焦点事件
        testArea.addEventListener('focus', () => {
            addLog('✅ 测试区域获得焦点', 'success');
            updateStatus('测试区域已获得焦点，可以开始按键测试', 'success');
        });

        testArea.addEventListener('blur', () => {
            addLog('❌ 测试区域失去焦点', 'warning');
            updateStatus('测试区域失去焦点，点击重新获得焦点', 'warning');
        });

        // 游戏键盘测试
        function testGameKeyboard() {
            addLog('🎮 开始测试游戏键盘映射...', 'info');
            
            const gameKeys = {
                'w': '上移',
                'a': '左移', 
                's': '下移',
                'd': '右移',
                'ArrowUp': '上移(方向键)',
                'ArrowLeft': '左移(方向键)',
                'ArrowDown': '下移(方向键)', 
                'ArrowRight': '右移(方向键)',
                'j': '拳击攻击',
                'k': '踢腿攻击',
                'l': '特殊攻击',
                '1': '随机技能'
            };

            // 模拟游戏键盘处理逻辑
            const gameKeyHandler = (e) => {
                const key = e.key.toLowerCase();
                if (gameKeys[e.key] || gameKeys[key]) {
                    const action = gameKeys[e.key] || gameKeys[key];
                    addLog(`🎯 游戏动作: ${action} (${e.key})`, 'success');
                    e.preventDefault();
                    return true;
                }
                return false;
            };

            // 临时添加游戏键盘处理器
            document.addEventListener('keydown', gameKeyHandler);
            
            updateStatus('游戏键盘测试已启动，按 WASD/方向键/JKL/1 测试', 'success');
            
            // 10秒后移除测试处理器
            setTimeout(() => {
                document.removeEventListener('keydown', gameKeyHandler);
                addLog('🎮 游戏键盘测试结束', 'warning');
            }, 10000);
        }

        // 模拟游戏输入
        function simulateGameInput() {
            addLog('🤖 开始模拟游戏输入...', 'info');
            
            const testKeys = ['w', 'a', 's', 'd', 'j', 'k', 'l', '1'];
            let index = 0;
            
            const simulateKey = () => {
                if (index < testKeys.length) {
                    const key = testKeys[index];
                    
                    // 创建键盘事件
                    const keyDownEvent = new KeyboardEvent('keydown', {
                        key: key,
                        code: `Key${key.toUpperCase()}`,
                        bubbles: true
                    });
                    
                    const keyUpEvent = new KeyboardEvent('keyup', {
                        key: key,
                        code: `Key${key.toUpperCase()}`,
                        bubbles: true
                    });
                    
                    addLog(`🤖 模拟按键: ${key}`, 'warning');
                    document.dispatchEvent(keyDownEvent);
                    
                    setTimeout(() => {
                        document.dispatchEvent(keyUpEvent);
                    }, 100);
                    
                    index++;
                    setTimeout(simulateKey, 500);
                } else {
                    addLog('🤖 模拟输入完成', 'success');
                }
            };
            
            simulateKey();
        }

        // 初始化
        addLog('🚀 键盘测试系统初始化完成', 'success');
        updateStatus('系统就绪，点击测试区域开始测试', 'success');

        // 自动获得焦点
        testArea.focus();
    </script>
</body>
</html>
