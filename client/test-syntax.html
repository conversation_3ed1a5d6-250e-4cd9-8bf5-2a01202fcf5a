<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语法测试</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
</head>
<body>
    <h1>JavaScript语法测试</h1>
    <div id="test-results"></div>
    
    <script>
        const results = document.getElementById('test-results');
        
        function addResult(message, isError = false) {
            const div = document.createElement('div');
            div.textContent = message;
            div.style.color = isError ? 'red' : 'green';
            div.style.margin = '5px 0';
            results.appendChild(div);
        }
        
        try {
            addResult('✓ 开始测试JavaScript语法...');
            
            // 测试Phaser是否加载
            if (typeof Phaser !== 'undefined') {
                addResult('✓ Phaser.js 加载成功: ' + Phaser.VERSION);
            } else {
                addResult('✗ Phaser.js 未加载', true);
            }
            
            // 加载phaser-game.js
            const script1 = document.createElement('script');
            script1.src = 'src/phaser-game.js';
            script1.onload = () => {
                addResult('✓ phaser-game.js 加载成功');
                
                // 检查GameScene是否定义
                if (typeof GameScene !== 'undefined') {
                    addResult('✓ GameScene 类定义成功');
                } else if (typeof window.GameScene !== 'undefined') {
                    addResult('✓ window.GameScene 定义成功');
                } else {
                    addResult('✗ GameScene 未定义', true);
                }
                
                // 加载phaser-main.js
                const script2 = document.createElement('script');
                script2.src = 'src/phaser-main.js';
                script2.onload = () => {
                    addResult('✓ phaser-main.js 加载成功');
                    
                    // 检查BrainrotFightingGamePhaser是否定义
                    if (typeof BrainrotFightingGamePhaser !== 'undefined') {
                        addResult('✓ BrainrotFightingGamePhaser 类定义成功');
                        
                        try {
                            // 尝试创建实例
                            const game = new BrainrotFightingGamePhaser();
                            addResult('✓ 游戏实例创建成功');
                        } catch (error) {
                            addResult('✗ 游戏实例创建失败: ' + error.message, true);
                        }
                    } else {
                        addResult('✗ BrainrotFightingGamePhaser 未定义', true);
                    }
                };
                script2.onerror = (error) => {
                    addResult('✗ phaser-main.js 加载失败: ' + error.message, true);
                };
                document.head.appendChild(script2);
            };
            script1.onerror = (error) => {
                addResult('✗ phaser-game.js 加载失败: ' + error.message, true);
            };
            document.head.appendChild(script1);
            
        } catch (error) {
            addResult('✗ 测试过程中出错: ' + error.message, true);
        }
    </script>
</body>
</html>
