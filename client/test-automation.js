const puppeteer = require('puppeteer');

async function testKeyboardInput() {
    console.log('🤖 启动自动化键盘测试...');
    
    const browser = await puppeteer.launch({
        headless: false, // 显示浏览器窗口
        devtools: true,  // 打开开发者工具
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();
        
        // 监听控制台日志
        page.on('console', msg => {
            const type = msg.type();
            const text = msg.text();
            
            if (type === 'log' && (text.includes('🎮') || text.includes('键盘') || text.includes('攻击') || text.includes('技能'))) {
                console.log(`[浏览器] ${text}`);
            }
            if (type === 'error') {
                console.error(`[浏览器错误] ${text}`);
            }
        });

        // 监听页面错误
        page.on('pageerror', error => {
            console.error(`[页面错误] ${error.message}`);
        });

        console.log('📱 打开游戏页面...');
        await page.goto('http://localhost:3003', { waitUntil: 'networkidle0' });
        
        // 等待游戏加载
        console.log('⏳ 等待游戏加载...');
        await page.waitForTimeout(3000);
        
        // 点击开始游戏按钮
        console.log('🎮 点击开始游戏...');
        try {
            await page.click('#startGame');
            await page.waitForTimeout(2000);
        } catch (e) {
            console.log('开始游戏按钮可能不存在，继续测试...');
        }

        // 确保页面获得焦点
        await page.focus('body');
        
        console.log('🎯 开始键盘输入测试...');
        
        // 测试移动键
        const moveKeys = ['w', 'a', 's', 'd', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
        for (const key of moveKeys) {
            console.log(`⌨️  测试按键: ${key}`);
            await page.keyboard.press(key);
            await page.waitForTimeout(500);
        }
        
        // 测试攻击键
        const attackKeys = ['j', 'k', 'l'];
        for (const key of attackKeys) {
            console.log(`⚔️  测试攻击键: ${key}`);
            await page.keyboard.press(key);
            await page.waitForTimeout(500);
        }
        
        // 测试技能键
        console.log('🎯 测试技能键: 1');
        await page.keyboard.press('1');
        await page.waitForTimeout(500);
        
        // 测试组合移动
        console.log('🏃 测试组合移动...');
        await page.keyboard.down('w');
        await page.keyboard.down('d');
        await page.waitForTimeout(1000);
        await page.keyboard.up('w');
        await page.keyboard.up('d');
        
        console.log('✅ 键盘测试完成！');
        
        // 获取最终的控制台日志
        const logs = await page.evaluate(() => {
            return window.console._logs || [];
        });
        
        console.log('📊 测试总结:');
        console.log('- 移动键测试完成');
        console.log('- 攻击键测试完成');
        console.log('- 技能键测试完成');
        console.log('- 组合键测试完成');
        
        // 保持浏览器打开以便观察
        console.log('🔍 浏览器将保持打开状态，请观察游戏反应...');
        console.log('按 Ctrl+C 退出测试');
        
        // 等待用户手动关闭
        await new Promise(() => {});
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    } finally {
        // 注释掉自动关闭，让用户手动观察
        // await browser.close();
    }
}

// 简单的键盘事件测试
async function testBasicKeyboard() {
    console.log('🔧 启动基础键盘测试...');
    
    const browser = await puppeteer.launch({
        headless: false,
        devtools: true
    });

    const page = await browser.newPage();
    
    // 监听所有控制台消息
    page.on('console', msg => {
        console.log(`[浏览器] ${msg.text()}`);
    });

    await page.goto('http://localhost:3003/test-keyboard.html');
    await page.waitForTimeout(1000);
    
    // 点击测试区域获得焦点
    await page.click('#testArea');
    
    // 测试基础按键
    const testKeys = ['w', 'a', 's', 'd', 'j', 'k', 'l', '1'];
    for (const key of testKeys) {
        console.log(`测试按键: ${key}`);
        await page.keyboard.press(key);
        await page.waitForTimeout(300);
    }
    
    console.log('基础键盘测试完成');
    
    // 保持打开
    await new Promise(() => {});
}

// 根据命令行参数选择测试类型
const testType = process.argv[2] || 'game';

if (testType === 'basic') {
    testBasicKeyboard().catch(console.error);
} else {
    testKeyboardInput().catch(console.error);
}
